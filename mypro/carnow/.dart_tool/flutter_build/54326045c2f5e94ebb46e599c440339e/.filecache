{"version": 2, "files": [{"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationselectionpattern2.dart", "hash": "8924d681363bacc7cd51c183b529f260"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_attachment/sentry_attachment.dart", "hash": "ddf7a3237ea322f4ab616071bc496ff3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/platform_interface/image_picker_platform.dart", "hash": "38982dc702bc4583fd29314508a32c17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getuid_linux.dart", "hash": "cc4abe2eecf823ea14c55f9c5c09e203"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/cast.dart", "hash": "dc379ed249557649f50b9c27d0033be6"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/network_connectivity_service.dart", "hash": "fba5ab0868be4878f4370759e393b95e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/wallet_operations_dialog.dart", "hash": "ef985012bcc294c4b28a6810c57083ad"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build-2.5.4/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "bce1bb799fa4cc899b6525721e14c9aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/scale_painter.dart", "hash": "47b4c68adbd4be2d7acceaf66d6aa09b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_traces_sampling_decision.dart", "hash": "05e9cac0fb2d101407ad3da7d190449f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/optional.dart", "hash": "7d49b944ccc5ee228590126488731a95"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auction/widgets/bid_stats_widget.dart", "hash": "d82ee067c67b7b7a15deaa499ecaba41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/platform_interface/file_selector_interface.dart", "hash": "5937c2b1cbdf77126bc2dd93570d3c98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/go_router.dart", "hash": "94124aa8c115b3bc8553ba80c419ceeb"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/Fontisto.ttf", "hash": "0e569864d25d0108eda475b7376c99d2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "b56cf23d49289ed9b2579fdc74f99c98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/behaviors/trackball.dart", "hash": "3cbc267c870b27d0a9681af53d2f71bc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/widgets/section_header.dart", "hash": "fbe1221b9ce0634f2c815297ee4dba64"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/inventory/screens/inventory_management_screen.dart", "hash": "7824fbbd9439e2013be6641d876ef9f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/wlanapi.g.dart", "hash": "30191f66ed437888e9e12cdc67d37c95"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/expansion_tile_controller.dart", "hash": "762d051a9c27d62c3767e390607095ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/constant.dart", "hash": "176c6b2c4f4e2d64cd55df2a0dabe5e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/es_messages.dart", "hash": "2c560be43d64cf41fc2a6a5da173149b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "68eb8647107febe1419211e153b27a54"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/models/admin_financial_models.dart", "hash": "10a6375ad054d02ca2f673e5762cfe42"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/clean_products_provider.g.dart", "hash": "2fced81e025c83cda99068bb10153fbe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/vm_trace.dart", "hash": "9a7022bcfa03c67d126e948062508201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/state_notifier.dart", "hash": "5bc3c944f62b4cf5d382a0c0e9b7e09e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/widgets.dart", "hash": "946e37d543d3912bef54a551fb02ea1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/src/messages.g.dart", "hash": "95bd0247422d589a2b39cd985a000749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationannotationpattern.dart", "hash": "d7be13ee7803d293bd92452e5ef3da27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/label.dart", "hash": "7de7aec8bf9b53488692403a3feb7672"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/products_result_model.dart", "hash": "dfdd6e3755ec3e0e42368770dd4df973"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "20b03effe92fdb82cb2b1efcf637be3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/file_picker_result.dart", "hash": "592fbd715117ae860b51b29d0be0bd50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/auto_dispose.dart", "hash": "39d249bfedd0655b147701ff81de4fa1"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/widgets/dynamic_filter_sidebar.dart", "hash": "90c86d4690d53cd3113de367639b5697"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_culture.dart", "hash": "75c8497164f5ba6846a7c15b895eaedd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "94c0c017ccb267b7cacc7c047ee5b9c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/slider_theme.dart", "hash": "b91a30e82a5c556445989053b06b571c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sqflite_database_factory.dart", "hash": "b2b96fda3b5d147408ecb71c2bbe73a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/web.dart", "hash": "d7c63cf2f303b7a0aef972ee03d3c7e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "956c84257f1efe6f10ab24f3d6702307"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "e8aae4779eccfdedd9c4b8cbce4ab952"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_auth_client.dart", "hash": "3bc24109049f63bedd0393f75bc23503"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "8fac1e5cad9ef06d9e55e6559c06b990"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/lib/share_plus.dart", "hash": "1b12343e6946a937c4cdc5b2e10651d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptors/imply_content_type.dart", "hash": "9955b767fdde0baa759d3431267e5ed5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/exceptions.dart", "hash": "e3ef71f241dd9711d599fc18b59abdf2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/feedback/sentry_feedback_widget.dart", "hash": "a0c266c49a8cb68947ee7364db5ba002"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/env_utils.dart", "hash": "d75f62f03297d8fada84de77f3e92373"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/google_oauth_config.dart", "hash": "7b290bab4f7ddcefc298a2c60f9d1771"}, {"path": "/Users/<USER>/mypro/carnow/fonts/Cairo/Cairo-500.ttf", "hash": "cbf0e8f4625a63746797ef8709167c44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/bs_messages.dart", "hash": "6ba111f5b4baa3239a925367cb3bbf9c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/util/jmap.dart", "hash": "b40fd62567e2b69677a3b6687cf351bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/invalid_sentry_trace_header_exception.dart", "hash": "afed6e9a5cfa209642f23f8721995bb2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "ca378f8a4dc93cea9ab759f410dcfdb6"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auction/models/bid_model.freezed.dart", "hash": "2e2cb06db78dfaf380a37f9d9381ec54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/flutter_secure_storage_windows.dart", "hash": "141745c6e29022622def8ba527cfd60c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/auto_dispose.dart", "hash": "34f75dd4788c48a2a7b2ce2efe4c51fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/test/test_flutter_secure_storage_platform.dart", "hash": "362bf1b65ae84f1129622a8814a50aad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/transport_utils.dart", "hash": "e2f45930c02833cb17b8193109a303b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_type.dart", "hash": "93f43c6a287e8cd98477a02e6aa0da8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/Users/<USER>/mypro/carnow/assets/images/placeholder.png", "hash": "83b57be3c88253ae08a43558c1715376"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/magic_number.dart", "hash": "d9d40cd4fd7e692ca4246d952d48cca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose_family.dart", "hash": "c8b1bc30159a132cab814de0d71e0462"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/visitor.dart", "hash": "87e0c94a0dd945f819a8bd24a9ac5e67"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "9d6f9dd391f828bccdbb47c5072c04c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellfolder.dart", "hash": "9595cf0e7becb4bf5de5d3a3865d631d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_message.dart", "hash": "eb54a5ead5cb8ea548f36e4b8780e4b8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "b692d4a68a086507a66243761c3d21a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_4.dart", "hash": "54d72b1c5b9977ccdcb6cd95e8acc7e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/mime_type.dart", "hash": "e26cb5bf5970055a9bd1828b524cb213"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "cd7f8dc942f5138db121aabbaba920ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestpackagedependenciesenumerator.dart", "hash": "21bea147ac9531ac715cd99a07f55866"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/format_exception.dart", "hash": "2128831f60d3870d6790e019887e77ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/font_awesome_5_solid.dart", "hash": "634bb49de0fa5a5648b80e4bfaa55141"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/web/web_session_handler.dart", "hash": "a1f409bfb79a8e5debee7a6a73252cd0"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/history/screens/history_screen.dart", "hash": "f29db21f0c515012f56dffef3902f962"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "cb745b78bdb964c02c1c4a843b9c1e7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/LICENSE", "hash": "96ed4c0b2ac486bba3db2c5d2a96afc4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_client.dart", "hash": "6b3c8cd4c0677edeb4fb8c22d923657c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/resolve.dart", "hash": "cbb8e1af9f1f0decfb6fc25a0725c51f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/pattern.dart", "hash": "cd6d0f606129e54552c5fee950e32bfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/timestamp.dart", "hash": "bca54a6e3c0b80a2300ab9ae4e9db4e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/native_app_start_integration.dart", "hash": "97766f3abfb1c18c4b225a4c8cebf859"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/repositories/repositories.dart", "hash": "71747b892f60f53d626d35db6bed03ab"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/EvilIcons.ttf", "hash": "140c53a7643ea949007aa9a282153849"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_sink.dart", "hash": "ef83fcd13366d1d61c5dbb5c6aae5ead"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/quantizer_map.dart", "hash": "42e37fab03a3ee2d526bb6f51ea4af23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/exception/exception_group_event_processor.dart", "hash": "73ad7adeaf75b8179e5e977f05a8095c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "hash": "e96fac214305f3ee9b39c4bd195836c1"}, {"path": "/Users/<USER>/mypro/carnow/.dart_tool/flutter_build/54326045c2f5e94ebb46e599c440339e/native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationcustomnavigationpattern.dart", "hash": "84de591d644b29f5e21052bd9c84263c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/defer.dart", "hash": "d06420fd88fb8f7cc3acc1643051178a"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/Entypo.ttf", "hash": "31b5ffea3daddc69dd01a1f3d6cf63c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/jni.dart", "hash": "95b22468736556f7a65e85386acbf3a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/cocoa/cocoa_replay_recorder.dart", "hash": "0cd6d1316a24c59c2bffef46753b0e30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/never.dart", "hash": "238c701652f1f5ad9ba928994a96e608"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repositories.dart", "hash": "8cf88d8eac6c9b46a6fb3877f9fc35d2"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/subscription_flow_provider.g.dart", "hash": "e90d0fce36a105131b03b033b81122f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/processing.dart", "hash": "0ca8410c364e97f0bd676f3c7c3c9e32"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/circular_chart.dart", "hash": "c9acc2a777b53901c0002fe65e171fb5"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/taxonomy/providers/taxonomy_providers.dart", "hash": "48521b13c9704581dceadae35cec85f9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "93576d7d8731bea65013886f9194df15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sdk_info.dart", "hash": "58c525247bbc028dab4e59e09bb5b2dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/value.dart", "hash": "bf3aeab9379cee97ddcc69d885a477f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "9c051d9a4098051ba8258eae9aae3195"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/flutter_vector_icons.dart", "hash": "b1ace9ce9f5110bc427d97d8f8a79c2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_subscription_transformer.dart", "hash": "9422bcb42f545a3d7fad54a0559effc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/import_mixin.dart", "hash": "50ef33e165498030b82cc4c8d8408597"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/utils/formatters.dart", "hash": "063e48b8d0253a279ad164bd17ff1bf6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclient2.dart", "hash": "48f954e66b945620e43ce8e9a8891919"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/FontAwesome5_Regular.ttf", "hash": "1f77739ca9ff2188b539c36f30ffa2be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_key.dart", "hash": "6a18f9347e6e639ebbbfb0a0ce98bb71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_log_level.dart", "hash": "273b01c838ebb5df8ad17b6e128ebfd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/builder.dart", "hash": "3131838b519fd1bcdc3486eb44d640d8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/method_channel/method_channel_image_picker.dart", "hash": "13b37731f32d54d63ecb4079379f025b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/resolvable.dart", "hash": "f7329cc0811af555900320e49bd9686f"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/auth_initialization_service.g.dart", "hash": "23be097217ad7888e0ca2aef5fda1cd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/state.dart", "hash": "9a453418cc0baa3cf4c4a41655f4a113"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "224c14ef0447e287cbae1b7aed416290"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/base.dart", "hash": "d7d24730943cbf47d39aa11425ebf344"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/inherited_router.dart", "hash": "94325c70d85d9b1d588018f56c56adc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/pattern_iterator.dart", "hash": "accb24637ddbe55d7a3f76e4618bdd22"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/log_printer.dart", "hash": "4576043706f693ac8efde372e73b23de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/circular_data_label_helper.dart", "hash": "f82e4d0eb6af2772eea97e8952ea7942"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/screens/unified_subscription_screen.dart", "hash": "d43942809369e79c80e64184cfb46f86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/character.dart", "hash": "c1d88c6f9a0dbed4be35c285ffac4da6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/string_stack.dart", "hash": "aa27dfc54687394062db977707839be5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifiledialogcustomize.dart", "hash": "859de35a02fbe705941f97e7700a3147"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf_static-1.1.3/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_value.dart", "hash": "21beb4ff2c06d1edc806270e0bfac51f"}, {"path": "/Users/<USER>/mypro/carnow/.dart_tool/flutter_build/54326045c2f5e94ebb46e599c440339e/dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_trim.g.dart", "hash": "cc98f385a5f6f64d392dbf03e1a9205f"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/errors/app_error.dart", "hash": "7d55f76cb91f4ae0761741bde1e8532d"}, {"path": "/Users/<USER>/mypro/carnow/lib/navigation/providers/navigation_route_provider.dart", "hash": "1de4fd70e78f251a2d6a7351fb053612"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_wsl_api_l1_1_0.g.dart", "hash": "f5defa76a8d0e0a0a5d1670fbc270cb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/lookup.dart", "hash": "b76abc07781824bc4c06d11631188db0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/MaterialIcons.ttf", "hash": "8ef52a15e44481b41e7db3c7eaf9bb83"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_message.dart", "hash": "1aa202f994383707f1816ab8d1478260"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_server.dart", "hash": "8580846ee9612281791cc377a99d0581"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_sink.dart", "hash": "89e6e01bd627dc1cc46b16ae027742dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/noop_log_batcher.dart", "hash": "5423a91b3a1a0d47d99e8c3f96e97110"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/timeago.dart", "hash": "2a55233cc5459751a43d7acce89b6b0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/lib/src/dynamic_color_plugin.dart", "hash": "cb0a97c3d20fba6250b6667498017db4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationlegacyiaccessiblepattern.dart", "hash": "15639b799e4dbb06ffd538d943964d19"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auth/screens/reset_password_screen.dart", "hash": "e06514d3203e7d47290ef9e22c107da9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "e37bb4fabbf2e61e9b7fbe06f5770679"}, {"path": "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "hash": "3046571f8d63bfe90de2bf69ae820d91"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "fab9f5f0fb3bdd9295e12a17fef271c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/client_provider.dart", "hash": "1fb6678dfa1a9978ef7d77fe88319211"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/typedef.dart", "hash": "ed5f51d6ce614e22dc0f16e0b1803196"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/it_messages.dart", "hash": "ddd22e86401c5255a26bd35eed0820ab"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auction/models/bid_model.g.dart", "hash": "9e0e26547f37194727fb157ffb50c987"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/windows_options.dart", "hash": "b4355b7f9f9e50017ce52a8bda654dd1"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/auth_models.dart", "hash": "8942834fe9097209620ee7e39bd59240"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/synchronized.dart", "hash": "044e7c8ac3258945fe17e90e1a4fff51"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f2ccd6eece375fce2e247d3995e45c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches/matches_iterable.dart", "hash": "037df9e7342fc8b812d985c8b6e8a0c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/following.dart", "hash": "662638321f1933cdab78277f222b8aa5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "dd3402d5403be91584a0203364565b1b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/providers/products_provider.g.dart", "hash": "c326743f6e7a914f00c9d98705cabbd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/file_selector_linux.dart", "hash": "25c44b3908d2602e0df540ca5b17da27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/functions.dart", "hash": "e999eca1c1c76717a74f50814d129d17"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "045c779ec8564825d7f11fbbd6fb2fa1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "5d7b0ee48c302285b90443514166c2d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-6.0.0/lib/platform_interface/share_plus_platform.dart", "hash": "6b1ca216873c5f4e0e145f0052ee3627"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/hub_adapter.dart", "hash": "f7c37997658964b3d39546dda4ea9974"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_service.dart", "hash": "da632f4b0e209fd38e988f5c951a424e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/android_options.dart", "hash": "2d04b343ac3e272959ffa40b7b9d782c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3-2.8.0/LICENSE", "hash": "f9a7098a030108fba87a81626c482aa0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/text_direction.dart", "hash": "45f61fb164130d22fda19cf94978853d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/maps_theme.dart", "hash": "be36985d28080e6f86077cc996372671"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_request_model.dart", "hash": "380132a4310f4ffba20434db73050952"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "7e0e723348daf7abfd74287e07b76dd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/kotlin.dart", "hash": "75eb68f72195720189d08a52952888d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-1.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "61d3c1705094ee0ea6c465e47b457198"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "5cedacfe2fd447a541cd599bfc1aef91"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/providers/simple_category_provider.g.dart", "hash": "a7bd73f5d71c64c6781ae5fd3ae904fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/utils/helper.dart", "hash": "7bcb4c1b1b99dc4882a06d597715ec4d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "67d5620f72c33680625822432b60b613"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "9e353a749332f6cfdbe6f0d07ff17f5f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auth/screens/change_password_screen.dart", "hash": "f1fc999f55af76cdf8db8fbbcd8d7d85"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "hash": "6303c4cb70f6ecfc6b7ee5a599a2c86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/propsys.g.dart", "hash": "c226787e49d4779d8fd575f9bf26e298"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/attribute.dart", "hash": "3a8ae5977fc932c86b4b61e92db7a275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/exception_type_identifier.dart", "hash": "c2200418539ee949e6e4a0ae1ed3ae80"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/routes/garage_routes.dart", "hash": "04e59eb4e7b41a9fbe625b5e28eecd78"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/exception.dart", "hash": "9a1e38007af02b923a4076ab4e989f48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation.dart", "hash": "fa2fa16f78792d714ca06eb1bbea9db8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/cached_network_image.dart", "hash": "dd2d618db009ed3aa82488ca3b0e1261"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ku_messages.dart", "hash": "e527b1ce072d160152032269b934a2d6"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/screens/sales_analytics_screen.dart", "hash": "206777fabf371053fad37ab47fe8060a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "d3b40ca9660164ac83b714d6e2df3843"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/mime_shared.dart", "hash": "c2f30f0829e63ccf0449de5982e324b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iconnectionpointcontainer.dart", "hash": "21a6eaa35ec3367210a47a559f54c4a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/uxtheme.g.dart", "hash": "14ca92a49cc066f7dbf04357098fef9e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/from_callable.dart", "hash": "b05a68b737792aa52eaaa4d3e093bb63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_stack_trace_factory.dart", "hash": "9ed0c34a5fbbd24419d14421c0274490"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/km_messages.dart", "hash": "28a4816855c345f70c0378c187a950ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetwork.dart", "hash": "57adb1ac7ff40f2fd9512ebf09281433"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/gauges_theme.dart", "hash": "894bc3a1aa9edd3782b5beaa0c73597f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "38df6f8cafb853c1acf0f6e6a4b4950c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_tracer.dart", "hash": "e4796390a45b75b6e6d812039fc970a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_match_rule.dart", "hash": "0298dac3221d4c6752b6207594e4f470"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/base.dart", "hash": "b21a009902949ddc4ba80d607867fcb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-6.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_info.dart", "hash": "2f3e8198efb4b9ec92c0126b25986acc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_profile_provider.dart", "hash": "1f695ad3157c1591b60720ef8a339b9c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienummoniker.dart", "hash": "3e2ba5ba60ae123aa45ccc5f07eb3ae8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/conversion_sink.dart", "hash": "efcbc6fd4212ea81281561abddbf29f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/method_channel/method_channel_file_selector.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getsid.dart", "hash": "5261078afe15bcdc637478bb6d7f7e21"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image_transformers.dart", "hash": "10404d098f485bca549850751b3e93b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/spark_charts_theme.dart", "hash": "2ef712346b75c1fc3857eab134b4ce49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/src/messages.g.dart", "hash": "f381ed91de52f40a7dff4d2f0f3f6d4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/exception.dart", "hash": "5934d7c0ee1ff94ec21aad560e9ed8ba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "a309d8ca64c3efb3ad74b742ffb0e1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app.dart", "hash": "dec43cdc695f6ef4f0a33ae459c0e58c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/dynamic_scheme.dart", "hash": "b7c07fdfc8aaa762f93fbaa316f728b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/base.dart", "hash": "62e6826075b4df67271133a79fc4d046"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image.dart", "hash": "e03a984efe74a058d3393aba7c55fe1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellingerror.dart", "hash": "c8ff0e27e7c87256a90d8a3ef24be6ac"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/location_provider.dart", "hash": "59337de7101da056d7ea57a41e95759c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/cart/widgets/cart_item_card.dart", "hash": "5f984ee8d8675418d1dced7a7b23068c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/debug_print_integration.dart", "hash": "a8b3475c25b127f5185ff53c4bbc7d08"}, {"path": "/Users/<USER>/mypro/carnow/lib/navigation/providers/navigation_provider.dart", "hash": "a732364acae8fa5bd938d3faefa6de66"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/backend_resilience_initializer.dart", "hash": "d4e5e306017d28b0c5f1997b69edad9e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "16859f5e798cf33fc3c76a7a3dca05d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/custom_lint_visitor-1.0.0+7.7.0/LICENSE", "hash": "ed9a041fe7435b9b67080b2e9b56f1a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclient3.dart", "hash": "e65733ef6887a9505fca66a22d9e2887"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/archived_chats_provider.dart", "hash": "5e758bf2ee1fecc3d872abc7cd548116"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/native_load_debug_images_integration.dart", "hash": "7c42507e8242bb7b6b6c741372b470a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumwbemclassobject.dart", "hash": "17399c5876a7f1c340f8814cbc903b10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/fi_messages.dart", "hash": "93c2f2419d5e20de88f9950cd4555354"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repository.dart", "hash": "d423d24bacc39262f492386b09a7ee7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/byte_collector.dart", "hash": "3aaf04a3a450c1b6a144f84f3c778573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/dev_utils.dart", "hash": "667c5e3e357a840e3d3a6137458c0c34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "52d0e96cbfe8e9c66aa40999df84bfa9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/messages.g.dart", "hash": "1567572a579e5f2aab31966d4a056855"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_fruit_salad.dart", "hash": "0663e41383137de0068cc7a8bd70f814"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "f350db07fdddbcfd71c7972bf3d13488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/labeled.dart", "hash": "715bccb8e9ba9889573a60bf0e457402"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_android.dart", "hash": "6f05b68df1b893e73008d1831589377c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/sentry_flutter.dart", "hash": "631882c6ac135e8863320848bf9c1e0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/letter.dart", "hash": "35ae3adcf5e51919e36509ef828107a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadataimport2.dart", "hash": "cb23738bdb6f2e8319ba8e9dac0072ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/tk_messages.dart", "hash": "d1053c5ba3ec6557e30ec634c0378181"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/LICENSE", "hash": "d229da563da18fe5d58cd95a6467d584"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/src/geolocator_apple.dart", "hash": "0190cf8d95873b9bcfdf00c1580334e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/combine_latest.dart", "hash": "0c4028018783c732ca451e7fff693d3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/chart_point.dart", "hash": "1daa9c9c25821857a762c9a4a1388e64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/sentry_native_channel.dart", "hash": "913af0998251255bf26a86b06a4e2c44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/tab_controller.dart", "hash": "c4f80bf20171189dfa08593d93caa5f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/environment/environment_variables.dart", "hash": "689ff7d200433709996de71e40fbe9ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/charts_theme.dart", "hash": "1cd72699ed31fdf82f65ecc9dee300d7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "ff7346c41b21457ac3ed3c623e6d9d26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/cdata.dart", "hash": "a1bc06d1d53e9b47b32fbdb4d323f44d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/orders/models/unified_order_model.dart", "hash": "89a27eeff405ce905e755a6f93e81b75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/pretty_writer.dart", "hash": "4c618cb90a20b93f23c554b8745d5f77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/view_hierarchy/view_hierarchy_event_processor.dart", "hash": "637d458d1e5a8254ef8050570c56fde2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gsettings_dconf_backend.dart", "hash": "0ab08cca5cf1835f92838ee85409a4e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_literal.dart", "hash": "8388d5e13155ebde873438c26dc4ca33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/constants.dart", "hash": "79164da6b57d9b3a95a59d01d7b8e64b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/sample.dart", "hash": "98ad95f9d48fa93a9cdc4a8fa0f69c73"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "fdf500742b45dff0abb3db9cbd350fd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation2.dart", "hash": "c98cadb2fac8ead45ecaa10366da3ec9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_geo.dart", "hash": "58bac0e33bfc7097ed0af1155ae55a2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sqflite.dart", "hash": "5c96fe82a9bf2dc00db9d93c2c0a41a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_file_system.dart", "hash": "dac02dc6cb13c753a5f3ae19976b1540"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/constant.dart", "hash": "84fdc97cdb402f94c301f5154682112f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "e88cac3fc4dc6a17d2bd13549d433704"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/providers/admin_financial_providers.dart", "hash": "b6836f6ef0f1dec39e34d139762de8bf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "47ccb32c843b4075a001e612853b2a31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelementarray.dart", "hash": "e7ee3c364551618835ecb4e3afe065ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/area_series.dart", "hash": "eb78f3601a61f0535cd9ea0f5f779cf1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/switch_latest.dart", "hash": "a52ae2e097914c25b04abb01abf02183"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_value.dart", "hash": "6f3422c300e4f005e63a4246631f3372"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/models/vehicle_data_models.g.dart", "hash": "0534dc5e9f6ff3602113dbd9614c1b92"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/new_chat_screen.dart", "hash": "b2b769107f87e20ce7e1a247fc7a92d3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "56198ea7cfc4930ad8bcfc81a2061b78"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "b3d31c9c130a73d5425905f361f63957"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-3.1.0/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/Users/<USER>/mypro/carnow/assets/data/automotive_reference_data.json", "hash": "615a30fdd84bff40d9991ab7b0ce6392"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/app/performance_main_thread_fix.dart", "hash": "9c91a7b5daa273bb4c20a1819ca97629"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector3.dart", "hash": "a1e740a70209acedc9ba1bff7141c14c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_rainbow.dart", "hash": "61f38162c831759dd3768aa4cf2d22f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/macros.dart", "hash": "8016baf49ccbce205455e3fc0bddbb17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/lib/src/geolocator_android.dart", "hash": "eb2dd79ede998c9cd76f7cf5e03a2ac4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/jvm/jvm_exception.dart", "hash": "e02bd989205924ce2534e9a55e5ff4c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "d942bc7ece253c7918e1f60d35e233b0"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/recommendations/routes/recommendation_routes.dart", "hash": "61142ae8e39f7a876a571437aa0b0751"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_span.dart", "hash": "ce92e8561e2534b201a121ba2e590160"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/noise.dart", "hash": "e9fe7ebb2a16174d28ca146824370cec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/base_cache_manager.dart", "hash": "53745062ff0e01e3d763823156d695da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumstring.dart", "hash": "68e28643e39694f628939978acdc52f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/selector.dart", "hash": "c771f26d18f9897af0e13e3a2c83d5ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/fused_transformer.dart", "hash": "4cbacf46dc43afb0d059b0016010f45b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "7755bff1bceea0db42330320ad10baad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/src/contrast_curve.dart", "hash": "82e64569c28ce30a19726e5c108b98bd"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/router/routes/developer_routes.dart", "hash": "16a3003e7312fbfe3a4b3497ce955965"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/any_of.dart", "hash": "8cd59827d2f99e2d6c62f2f38c275cf5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "04c713cbc0ac5e15c7978a2e91b81488"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/chat_storage_management_screen.dart", "hash": "dc2ccf6918bf30fcaa19d5c382e0afc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_level.dart", "hash": "9676f9bde24c1d9a0498cf2337cd9ee7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationexpandcollapsepattern.dart", "hash": "8d6e1950b64962d48ef050d9517791be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/src/messages.g.dart", "hash": "3ff09a7edec90fdf07e59bc3514ea474"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/predicate.dart", "hash": "9d95e55b0ed6080e677989c4e1e1cff6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/dbus.dart", "hash": "59ba4a85ea18ab7b3030f370a0e93450"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "d0ab7f5e11e48788c09b0d28a0376d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/web/javascript_transport.dart", "hash": "25f1a572f2da060022d453e99a7d139b"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/models/user_vehicle.dart", "hash": "ef9d6c4822a8da9759e5402ea8d622ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/already_subscribed_exception.dart", "hash": "6f236f4f809dcf6f1959e9536fbf1f18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/taxonomy/providers/taxonomy_providers.g.dart", "hash": "8fbe1c6e6f4e93bdbfccbba3ca762008"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/base.dart", "hash": "737fc999d5d26218c34c7423fe061f1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ur_messages.dart", "hash": "c5338935b45474ea94cf777a85c4bb73"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/history/providers/history_provider.dart", "hash": "2f6c39ce8f719aa6003e4136024626bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_log_attribute.dart", "hash": "c9c8ebe63c76787d61f5ae90e520c738"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_devtools_extension_data.dart", "hash": "3f47c1f73c7a4541f98163b83d056456"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/file_picker.dart", "hash": "a92be5f2cc144f98f85448fb4b2f0864"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/url_launcher_platform.dart", "hash": "0321281951240b7522f9b85dc24cb938"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/types/auth_messages_windows.dart", "hash": "c88d29163ef68ff67276b64f3a1331cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE", "hash": "612951585458204d3e3aa22ecf313e49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iclassfactory.dart", "hash": "adbacdd68acdd5e35ce91a3475a1be38"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/notifications/screens/notifications_screen.dart", "hash": "03cc348f15994b1b802e83ce88aa43f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/path_utils.dart", "hash": "e335de991d295627ccaabe152db13f68"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/admin_dashboard_card.dart", "hash": "213a48ecde3d83ca295f806c8946c3fc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestapplication.dart", "hash": "bc01545a1cca050f2067c0b6163a4755"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "3ad691d7f4e0dfc9bac177f56b288925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersistfile.dart", "hash": "0f1d84a9023a931b4b3cda6b907d76e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/linux/kdialog_handler.dart", "hash": "0832a69737f4ea7241d597516880d3b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/AntDesign.ttf", "hash": "3a2ba31570920eeb9b1d217cabe58315"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/exception_cause.dart", "hash": "fc06bc67373860c386049ab16c0bb11a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry.dart", "hash": "66c3d8022ecd26ac3b2f30fe28e4c475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/fonts/Cairo/Cairo-regular.ttf", "hash": "45aaa2b5f9de1d61c2d3fe1f40107ac8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/LICENSE", "hash": "2a68e6b288e18606a93b3adf27dbf048"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "64a2ea17e8058aec30096102af030f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib/app_links_platform_interface.dart", "hash": "1650dcb22227d2d7520c279f9b934775"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/widgets/product_card.dart", "hash": "e63a6e56939f588bcafc29d10e2390cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "728c8c2ffdc4b584c67df65b41e6461f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/layout_handler.dart", "hash": "6d37091fe6a70543a5ad473f9d6f6cf1"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/auth_provider_initializer.dart", "hash": "2dc31fd14caa41e5293ebc0c4c1ae642"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "6189af9ddf633811ffb6414cb9d3f744"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/enums.g.dart", "hash": "ebee8885b5afd397cfa8920eeccf88e6"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/reports/screens/sales_reports_screen.dart", "hash": "1cc8ee870de217e09c1a8eee1d7ceba8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immdeviceenumerator.dart", "hash": "8a2e692d7adcf4c9e0bd0f85979ab7c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/riverpod.dart", "hash": "9518a1e0696846221033c0434d777377"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "138038335aa2c209f231b2694d5aae3f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "0821fcdff89c96a505e2d37cf1b52686"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/method_channel_flutter_secure_storage.dart", "hash": "20e7221c12677486628b48b0c30569f8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "fbfdd6181c7ea8d5950c24b467debf31"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "28e91fd9077820e2cb2eb981471636ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/structs.g.dart", "hash": "b248aab8f1807ae07bc22c26210f2def"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/async_selector.dart", "hash": "c050fb9d5c851547735cf2c46d8b6288"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "c7c757e0bcbf3ae68b5c4a97007ec0b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/base.dart", "hash": "0ab8c6ae2a539e1eee8cc8e4e7cac2d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/auto_dispose.dart", "hash": "a3250d5fb60cc8b17997c886a67be737"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestproperties.dart", "hash": "25ff828118233f5852e97c3e15c2a5da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/throwable_mechanism.dart", "hash": "1b47e8544bdca691a01ef56c27bfa4c7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "91f73f40856927e688e1707a923db3e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/quantizer_celebi.dart", "hash": "88b38af8a6ab0efd56d1fb06883c44db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE", "hash": "c17706815151969aa7de6328178cc8bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterable.dart", "hash": "0ea87086ab38d0a0e292321e807293f8"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/wallet_search_bar.dart", "hash": "617712b99de974b94d25cdbc4c4da82f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/node_preamble-2.0.2/LICENSE", "hash": "041649b8894d85f501d01b8e3946ec3a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/home_screen.dart", "hash": "9dd45fd2789f30534f46c3bbd9aa56c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/form_data.dart", "hash": "bfd57391197129cbe3c47c75b2c21672"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/FontAwesome5_Brands.ttf", "hash": "3b89dd103490708d19a95adcae52210e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/result.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/daterangepicker_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/delegate.dart", "hash": "087515340a18e957de353a2f6fa77893"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider.dart", "hash": "d28de61067df9bc3d509be84deec1140"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/renderer_helper.dart", "hash": "6bb6a5669574b0eaa5648f5535b72fde"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/flutter_exception_type_identifier.dart", "hash": "c191b3a772fdbc0fdc5be646782bbff6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_model.dart", "hash": "6571dc8536a914e0e577361c2525e249"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtreewalker.dart", "hash": "034536c8c0bdfd72d8f8060ea1f36f3e"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/auth_initialization_service.dart", "hash": "b81dfe046c7055774d69404c28593e69"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_makes_model.dart", "hash": "24b4dc1e7ab710c02a647114837e6f34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_8.dart", "hash": "9dbdc6f759d68f1ba1b47b561de1e299"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/errors.dart", "hash": "dd481a9d1b5a5ac7edaa1596700c7f39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/platform_exception_event_processor.dart", "hash": "7004001a9c0dd689f4bb06c6aca2a9b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "fddd73db94bb2fa3a0974bed845f32a8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0e13760edcb9f90f659ba77c144a3461"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_permission.dart", "hash": "2c1328c414252b20b52d7e1c8505d81d"}, {"path": "/Users/<USER>/mypro/carnow/pubspec.yaml", "hash": "57e7a54804104e276aca2056075fa364"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/parser.dart", "hash": "a54725bc16ee2ca993762c441542c1cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/config/backend_config.dart", "hash": "e8253d98e8ae8034ef39701f3f6c3c5e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "952fb243dbdb00bfe11b0293238b115d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "caad40ad1936874ea93473b300bb909c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/auto_dispose.dart", "hash": "9eb3cf0f33c573aa9e8424441db78539"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/image_picker_ios.dart", "hash": "75290287531fff47b4eab2f25f050d57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hooks_riverpod-2.6.1/lib/src/internals.dart", "hash": "1764cdc6a8380ff93bdf6b71f094659b"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/admin_subscription_provider.g.dart", "hash": "0a20e006e93a9c56ab7c11b18b1bb1a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/separated_list.dart", "hash": "031cc72717282ff9f7f588e73fb0fd0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/macos_options.dart", "hash": "ef56d0c30c2ebbf770de5c7e9cd6f6a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/list.dart", "hash": "ee730199a496cacbfd82312849e80523"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "837da7ede58523b5aff0ccbb40da75ba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/multi_image_picker_options.dart", "hash": "5ad1b4844df9d51e4c957f292d696471"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "55f7619e20765836d6d1c7001cb297fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/flutter_hooks.dart", "hash": "9e0e17710b64bde271789778e227ce7b"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/interceptors/error_interceptor.dart", "hash": "579d4ac439c4581f95e4d8e336aa078b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/focus_scope_node.dart", "hash": "462e14d1be0222e48a3d9dfaf0dcdca0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation5.dart", "hash": "d879c3156e19f2b290c4d6eed1de5e89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/mechanism.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "29d1f8b59096b4d11d693c4102a08499"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterator.dart", "hash": "4d16182e94ac3ec4a2804eb97efe7842"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "095edf197865d16a71124cfaa427e31f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/models/simple_vehicle.g.dart", "hash": "f8dfd52e76da208d1fdddbbef2259464"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/binding_wrapper.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/LICENSE", "hash": "abaf6e315e8ecf1eab16784da6742401"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/models/user_status.dart", "hash": "9219882d2c0b33249c3e979760efad1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "3e8df17480fcb123b3cdc775ca88dd89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/slide_effect.dart", "hash": "9ae1bada4b57958b80b63141bf9c4076"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_item_model.g.dart", "hash": "876d0a0770bdfed5357de49223e06ec1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/_internal.dart", "hash": "ef4618b5bf737a7625f62d841127c69d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/settings/screens/settings_screen.dart", "hash": "e41b9f267955598ed783fb7bea26d862"}, {"path": "/Users/<USER>/mypro/carnow/fonts/Cairo/Cairo-300.ttf", "hash": "fa2b785b431fa365329cfbf3955f1b88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/pdfviewer_theme.dart", "hash": "aaba4d16ac53ad6e9eeb241292cb1ff2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "826b67d0d6c27e72e7b0f702d02afcec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/patrol_finders-2.9.0/LICENSE", "hash": "dc0ed204c1849749ac31938318b1a97f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "2c5021ff8faa0330f66b1c501e8d4b22"}, {"path": "/Users/<USER>/mypro/carnow/l10n.yaml", "hash": "ee5690cb05eaedc99d9a3e95ca6718b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationproxyfactory.dart", "hash": "5d461db74d04d7e270d13a5a8a340796"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/lib/src/types/android_settings.dart", "hash": "bb4b92648ab395eb8a548dc2114e942d"}, {"path": "/Users/<USER>/mypro/carnow/fonts/Cairo/Cairo-200.ttf", "hash": "b95de133534ef622c7594cd0c94dfc25"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/assets/data/body_styles.json", "hash": "b9626ee4b734c533f0b3c50df21acf62"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/apple_options.dart", "hash": "d4efda9ec695d776e6e7e0c6e33b6a4b"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/router/app_router.dart", "hash": "56f3469d288834594950cb78f20b26a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_operating_system.dart", "hash": "7d2c169875be0aab7b9a7d87c8f0ce7c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/indicator_effect.dart", "hash": "f21a7258d54db2616bc54cdb773f6865"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/whitespace.dart", "hash": "f0df878443ef28db864b73e66f8206a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/cdata.dart", "hash": "008d33cc2aea11e7921ee238469947b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishelllink.dart", "hash": "7132bdf47eb7567294754da6caddbe14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_remote_object_manager.dart", "hash": "69c08243f2f74c58d6ad38b17bb5cb9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/src/event_types.dart", "hash": "376912ba1f54895e09a77a2c0e766b8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/factory_mixin.dart", "hash": "47258dc751a1217744986101e934f62c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "0e3d746a279b7f41114247b80c34e841"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/irestrictederrorinfo.dart", "hash": "a42121307a3d24f06691ab35f935206a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/method_channel_helper.dart", "hash": "cc0346cee948c43689ab7c51a0e4939c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4a2215ab704d09e97121c1bb71942b3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/where.dart", "hash": "bab2294ec70ff137aca684dd19203943"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/utils/unified_theme_extension.dart", "hash": "4c96243d01aa2390c1019154231f124f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "7bdfcadf7dd131e95092d30909e5b11f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "c98d71a32518e80bc7cf24b1da6c9c57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/LICENSE", "hash": "39d3054e9c33d4275e9fa1112488b50b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/sparkline/marker.dart", "hash": "412c952a31295538a056afab5439ae1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/constants_metadata.dart", "hash": "201005c585ce255343e625b1a5e49601"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/nn_no_messages.dart", "hash": "d1e77eca0beba7d2e73139ec28373781"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/simple_line_icons.dart", "hash": "fa71ce45b181a0fd7f51b10546bd166a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/sentry_http_client_error.dart", "hash": "4a3f65cd5f29fb80ad375535c716aed8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/web_helper.dart", "hash": "60db0a181494c7db06a18464e2d6e796"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/method_channel_connectivity.dart", "hash": "3d18e1306d78e114f98c9dc311fbf158"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellcheckerchangedeventhandler.dart", "hash": "0e619c36f088b986b65eadb12698abb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE", "hash": "87ee25bbef5b7cb7dcb056c3ec20f243"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/LICENSE", "hash": "aca2926dd73b3e20037d949c2c374da2"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_stats_provider.dart", "hash": "9e0b22d1b7c5c248911dcefe20740262"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stack_trace.dart", "hash": "bd15738d49bec303fe3d234de40503d8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/token.dart", "hash": "6c8afaf3db5be20a458530a92ff971d5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/sphere.dart", "hash": "d1089412c69c2ca9e4eeb1607cf0e96e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellchecker.dart", "hash": "556c5677ab197ac52aaee6e02d6ebd70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/io.dart", "hash": "a45632c7d0440400b3f7a2ce615d21c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxfilesenumerator.dart", "hash": "c72923f8ad46feb8bcf25ecbd0379294"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiocaptureclient.dart", "hash": "187bca624cdda52a572fde54e8395124"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/screens/admin_management_screen.dart", "hash": "ffe5eb86b363d04c71bef534006c5119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_transaction_name_source.dart", "hash": "ff1bdfbdeb2980ca578db680bf476356"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/runtime_checker.dart", "hash": "2bdd9744a9bd6bda2b24c6eabc62c805"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/backend_resilience_service.dart", "hash": "02273cabbf5462822c76092f03441e4b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "f26e2cb53d8dd9caaaabeda19e5a2de3"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auction/providers/auction_provider.g.dart", "hash": "d78d09011a7622a98e04a1da3da0d2c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "hash": "bfc483b9f818def1209e4faf830541ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/load_release_integration.dart", "hash": "16b4f96618b8850f46bd9709ea829c12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/en_messages.dart", "hash": "5be1b84d7225a777b716740a573005e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/structs.dart", "hash": "b51cea8017e3cbb294fe3b8066265c7e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/client.dart", "hash": "8584d1850a1ff465be311bfc3e1361cb"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/MaterialCommunityIcons.ttf", "hash": "b62641afc9ab487008e996a5c5865e56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/listenable.dart", "hash": "a5bfe2d6591e761bf3c5dc0cd4ded99a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/chat_settings_screen.dart", "hash": "8aee5e1517f28ace8842dcaa09b2260f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "384c15d93757a08ae124e6c2edeb4e9e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "2553e163ea84c7207282c18b5d9e14c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/range_selector_theme.dart", "hash": "c6ffb53ef6c38f6972ab60ea2233fbd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/implementations/method_channel_geolocator.dart", "hash": "f236f79ad83d0fb0b86b75561ef1d4d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/kernel32.g.dart", "hash": "6bb547ebfa35dd1c7acaa81eedf39905"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_runtime.dart", "hash": "ca0d9906c07220e41a0e80adb8893c1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_visitor.dart", "hash": "61e938fe770ed7331e39f1dda1b64dd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/lost_data.dart", "hash": "3bc26601d19fa0f119ec8e7fc5fd6e23"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "245a31a30063b63cbfd631fdc2ddf0d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/flat_map.dart", "hash": "115640739fe47a728c4b1c3a4b4c3506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mockito-5.4.6/LICENSE", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_save_location.dart", "hash": "3c21d269eae774b7e06b8adbe73aa18e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_annotation-2.6.1/lib/src/riverpod_annotation.dart", "hash": "d2993fa3a07deac215ae9cd03c8b45f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/src/point_provider_lab.dart", "hash": "28b2af7f1216ccd38b109a03e06c2831"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/debug_meta.dart", "hash": "ff190508f170cc08d3f49160a9717702"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_attributes.dart", "hash": "f1acbe1ea51585adf0a1ba560796bab1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/value_stream.dart", "hash": "4ec7181b3b281703a8fddee43b540ee6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/material.dart", "hash": "8ef67f192314481983c34c92a81ee5f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/winspool.g.dart", "hash": "18e255eb54fef40d17b6f6abac4455aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/compact_number_format.dart", "hash": "4d3e899568e228c77a15b84754705d4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/l10n/generated_date_localizations.dart", "hash": "26bb5716eba58cdf5fb932ac3becd341"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date.dart", "hash": "f36568b4288388242cb6f7775cb60c42"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material.dart", "hash": "76611c76bf37be8fc89798858b6c7685"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-1.0.0/LICENSE", "hash": "b3896c42c38a76b4ed9d478ca19593e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "48a02b5ec3a8c6127b28927b5960d076"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/electronics_details_screen.dart", "hash": "10f10375930d42043d375ab65a4064ff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/patrol_log-0.5.0/LICENSE", "hash": "dc0ed204c1849749ac31938318b1a97f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/memory.dart", "hash": "647e49fd7e2b6707e82858420b630c46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/comctl32.g.dart", "hash": "f203522611d9d5ac9047af433e7f84f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/trimming.dart", "hash": "1db476563b55e241003667ca3669c0b8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "6b396237a38f3417babe500724de8a84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jimplementer.dart", "hash": "6975b87f5bc1afda8d6f86375d97aedc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "3cd5a71cfa881a4d3d6325d6b2c6d902"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "35e99597a2bc1839b114f890463b5dad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/barcodes_theme.dart", "hash": "77f7b68f9ab118ef5f8248b6d7ca654e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/barcode-2.2.9/LICENSE", "hash": "5335066555b14d832335aa4660d6c376"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/LICENSE", "hash": "9741c346eef56131163e13b9db1241b3"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_enums.dart", "hash": "f1f74740b52b5d401bc607759a25852b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/range.dart", "hash": "57de88edbfb0d8951419051441a6de56"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "329bc189be2701d02fb1b7975ecf329e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_model.g.dart", "hash": "3ee69b9e874e7faab3755762790afd0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/linux/qarma_and_zenity_handler.dart", "hash": "ad4278f0f0d95649281335b182fc6fe4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "62a38b21e9ef4b8a8d5ae1db1c355bd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/none_of.dart", "hash": "2080f99186cef2d2ec3f4c6c5b7c768b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "355538055d623505dfb5b9bae9481084"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "0f717ff4ecfdaa0347894abbedd5d1e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextpattern2.dart", "hash": "1dfa85bd16bf08ae91f9cceb02ef1563"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed-3.1.0/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib/sqflite_android.dart", "hash": "3d09396dae741c535c293314adc09565"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-7.6.0/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/normalizer.dart", "hash": "b6fde0bb78218226247a2173dbf96ea5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "c069ad8b31e18adb75c27530f218957a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_measurement.dart", "hash": "168f304e3c0705849cebee06da0c2770"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/src/identity_types.dart", "hash": "4c94f3d3ef437c1467b35fe45da460aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/writer.dart", "hash": "21a6f7aab6021cd2c8c69f9cd78ae36d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/screens/category_products_screen.dart", "hash": "7d503cb4cbaa7b4ab4b21e53debe54db"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/screens/user_actions_dialog.dart", "hash": "2a667a86d8226ed899854928e0ca7f74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/parent.dart", "hash": "210257ed62edd783098ed34d7cfb0204"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/download_progress.dart", "hash": "1e0f86acf6978afd1769e17506893606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumnetworks.dart", "hash": "6e3924fcfcaa29ba9146915e7603139c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/wallet/screens/modern_wallet_screen.dart", "hash": "fbbe1cd29dafd0f95de40361078268ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/annotation.dart", "hash": "3f69cca99f239a097d38f694068203fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/base.dart", "hash": "63b92eb56c14d5474db11677f1800c83"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "31b0d2bf647a0ce615f4937dd5307b1c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "501bafdb6d3784f18f395d40dfa73cd2"}, {"path": "/Users/<USER>/mypro/carnow/fonts/Cairo/Cairo-800.ttf", "hash": "6469ba6c1b59b4f2b07e49f516ccf664"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework.dart", "hash": "d856ca958740bf8a240738ad9e9e69c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/src/flutter_secure_storage_windows_ffi.dart", "hash": "2ca4b9e92c39403717d2dcc32bed57e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/technical_indicator.dart", "hash": "b1650f320fbefd6974b2525ddec09899"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "edd2f9cabffc7ea6a5a9497a1b1beccd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/rometadata.g.dart", "hash": "87ac4b62f17065d7456bfb6f6ec0a624"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "f6d18a38c0986111a3d297424ed6fbcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_message.dart", "hash": "c66b0ff23c4a79cd78469d267761a29c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_measurement_unit.dart", "hash": "8f869d4368c8d725543b0cd4378bb7dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/listen.dart", "hash": "4990e198f887619ece65c59a3de67869"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "ac902f2f74549f89e0be0f739d94f7f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/file_selector_macos.dart", "hash": "20f3c0d39cbc5c2fdb223745edcecdec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c9105f08cb965dfc79cdbe39f062d6c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/util/util.dart", "hash": "cf2b42c94ab97ce116e195500f147944"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/widgets_binding_observer.dart", "hash": "0c70491bc3a34f8ed121378eea379017"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "4d673eddc0bd2289539b66a92faae868"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/node_codec.dart", "hash": "1de9311ba0f47dfc96166daab936f705"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/options.dart", "hash": "fd4b31aeef96e63881bfcd44031ae269"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/min.dart", "hash": "ecfd8a09746c8bbb7b51d4741fb4645e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_flutter.dart", "hash": "daf501f756478bdb70fe680c4ff8d6a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/view_hierarchy/view_hierarchy_integration.dart", "hash": "9122f710b8894990dc8abbba40da5be8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/family.dart", "hash": "c32553850c6990014c017cc3b3024df3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "9c13d1f810b039faf38c54f062c83747"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose.dart", "hash": "7c89e8d3e17b2ff04570b741ce311e44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_last.dart", "hash": "08f42ef74f129fde820b3414026b8d34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/logger/sqflite_logger.dart", "hash": "d3f2ace1407cd47efe55881d7060408e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_span_context.dart", "hash": "3f0b95302b6570d5c0cb98ebfab2a220"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/hub.dart", "hash": "0239032bd2cec55d8a0dd468fd04c103"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/SimpleLineIcons.ttf", "hash": "d2285965fe34b05465047401b8595dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/src/connectivity_plus_linux.dart", "hash": "2aea038844961a04f31f81fbd8503cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "0763a220fcb5274b6c228b8b440ddb2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/LICENSE", "hash": "b93fe5bcbea09a4ba86ec85c6bb8baaa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "166478d231aa67eb8e47a7b559955e6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/bluetoothapis.g.dart", "hash": "21dfa823454d051c097b62eb7499f71c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "4da5ad5941f2d5b6b3fbb3f7ea217b41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_6.dart", "hash": "7eaf5b7e19afccfa1bde4bf16bf53648"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jstring.dart", "hash": "e2ade3c595966f6c57c8e83132c8980a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/micro_money.dart", "hash": "391b7eda9bffdd4386292eae157d449c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/connectivity/noop_connectivity_provider.dart", "hash": "38028420918cc3e578d37141e6e46eca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/node.dart", "hash": "9ec244272cb6c8da46a6dd5f104f0dfe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/orders/repositories/unified_orders_repository.g.dart", "hash": "cc7e5ffc1ab53d0bac1517d04fb042b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/feather.dart", "hash": "93e9e1bc1217eedc46462799c338cfd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_remote_object.dart", "hash": "4f187fc37cb2a7eedf4681e2321792f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/platform.dart", "hash": "b92ed901e8df2fde6d4739ed5e59051d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/nio/jbyte_buffer.dart", "hash": "19c5773bbbb442fd77dda91a42eeb048"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/Octicons.ttf", "hash": "4bab0c61f141522a17c97148aee365f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name.dart", "hash": "da50c399c40281c66d3c2582ac225276"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_exception_factory.dart", "hash": "d93874aed85519b313aad0a490e3156c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/factory.dart", "hash": "a79e2b9a182eb762fadaab05e9269edc"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/recommendation_api_service.g.dart", "hash": "91d55829b19973bbeb335720ebf1228a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stepline_series.dart", "hash": "62c76c6e2085da833e47f741bba85613"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/local_auth_darwin.dart", "hash": "2d0b26be5c4a3422283198cf7c138dbc"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/screens/simplified_add_vehicle_screen.dart", "hash": "fb75ba30e4da64066d8ad9d853ed57dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/windows/file_picker_windows_ffi_types.dart", "hash": "6d9bf9e41acfef21e5407dced2080155"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/scheduled_recorder_config.dart", "hash": "09bb5348b3804c1b9bd0b57a5af96fcd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/provider_base.dart", "hash": "ddbfb4de9e9dc40a09a6bfae74a41dd8"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/router/routes/cart_routes.dart", "hash": "50a79693fabbb52989eee18f5661c542"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/types/auth_messages_macos.dart", "hash": "03646ad7ecc4178fcf9509961aa7e424"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/widgets/unified_card.dart", "hash": "c9a4016074c970d24592e13bcde282dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechaudioformat.dart", "hash": "f7b5a54fb6f6b69cc4234a97ce7977e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier.dart", "hash": "a67d1346ef152a92e983a9d7dc1a96fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image_handler.dart", "hash": "a9ad1aa35c1b9117f15a379ef03480dd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "74a89d22aa9211b486963d7cae895aab"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/models/product_filter_model.g.dart", "hash": "7121806365079a9a51e607ca3c92f085"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/activity_item_model.g.dart", "hash": "c65b8579d9e6941f8d9293bf76477bca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/advapi32.g.dart", "hash": "e1c4eba9ccd9a12c58e4e531e73fcc32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/lifecycle/on_before_capture_log.dart", "hash": "0276ec25685d21fc9b6f06b0bc2b0c8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parent_exception.dart", "hash": "2ede71f09a240decbc57417850f8feb7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "81fd3ef494f4443fb8565c98ba5a9ba2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "dc2cfe4408f094916cd5eb1d294d1f2f"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/AntDesign.ttf", "hash": "3a2ba31570920eeb9b1d217cabe58315"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/octo_set.dart", "hash": "0d750078c87ce8f99c60c3c76305c11a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_baggage_header.dart", "hash": "59960e9fc54d14fe88b0854281bef986"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/widgets_flutter_binding_integration.dart", "hash": "e335030b01ccaf32448b0c3ea58714ed"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "9b52b890a7d94fe05f5f3ab8b7324b35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/ant_design.dart", "hash": "d094ac58caf2bc4cf5c47308d9415502"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrange.dart", "hash": "46d014f5f4ff404b81098da9b003b770"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/providers/account_provider.dart", "hash": "b5d9e1d11aae7c46ba37ba93bc6b5a84"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/widgets/loading_widget.dart", "hash": "f1e7532f684695eb3ef3826e92995ee4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/native_scope_observer.dart", "hash": "49afd145ac27e0f98dfb9bc82decc120"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/providers/spec_filter_provider.g.dart", "hash": "99e974beac588791b4d0a696c883c8ec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "a69e90f683dddaf61ae8d7f094219026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement.dart", "hash": "e00e5a90ff913bc9c53a6572e53ec576"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "02f1d44813d6293a43e14af1986519ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/LICENSE", "hash": "ef2f4f5048c86bfd71a39175b6f103d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dislike/dislike_analyzer.dart", "hash": "052660a52f5ab94091182bccabab6f84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sql_command.dart", "hash": "4e7b4cf98b7ea45960f7d79fffac5705"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/services/product_api_service.g.dart", "hash": "9fb0f5e80e30829bad0e7ffc87e78667"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/definition.dart", "hash": "8680f57e6ae9665a5f051c06c1efc688"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemimagefactory.dart", "hash": "d04edc39b6d3477197606ec9c969e738"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/run_zoned_guarded_integration.dart", "hash": "66e95a7e8b95f6ee3c683e6244c3116c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/string.dart", "hash": "1aaa0309ba77b0f57733e99543c455ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_parent.dart", "hash": "a7ac3293430577fa9c028b0df6607fa4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml.dart", "hash": "2c4c36a5cc838977cf822b6db5d9200a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "abbe93b36782df11e43e348dadf52e94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/non_storing_object_provider.dart", "hash": "21cb059be81989938ccfbda405ae9a65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_isolate.dart", "hash": "7159a22903ab0ea273f3f4c892ae7d67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/FontAwesome5_Solid.ttf", "hash": "c867d50f0b1444a9d91e516e0f51fe14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/geolocator_apple.dart", "hash": "517523644fe678d1dedbf87f16686848"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/frame_callback_handler.dart", "hash": "715edb11ec810ad00e0b0f1018113882"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/character.dart", "hash": "f6f8ad33193db66deb89d68e406eeaf9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isequentialstream.dart", "hash": "2d06e55a087b389063f0d5777e1d8563"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "b61a261e42de1512c8a95fd52ef6540d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_view_hierarchy_element.dart", "hash": "140f385f3fa22698fb0011cc6b6bab34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "43ba6279385eca1e9d14a3e4d020a3ca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "72bbc3da5da130fb11bb5fc65614653c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1/LICENSE", "hash": "7c7cba8d9e0619db0f3d2fd336c7cd96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/bidi.dart", "hash": "432ff5976b2e0c85f249933d757d0e5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/window.dart", "hash": "b081e406a9e3448ff172ab7d21f31f7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/trace.dart", "hash": "dcb1bf21d8afb364e20a47f106496780"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/platform/platform.dart", "hash": "7f98c1f297dc85272d200318554bfa64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/lib/geolocator_android.dart", "hash": "28039d2a949dbc017a05ba34280698d3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "e3127548d819af5ec9ecb10b5732b28e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/hct/src/hct_solver.dart", "hash": "2972206c70139c2948530bbb5c9cfa27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/primitives.dart", "hash": "d93dcdf81f72f235d1826a1f57e96a35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_address.dart", "hash": "4ecc0e7678d4ed3bf62a04b3e383e424"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "dd510cd97dc23d22aebc7b60affd6329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_auth_server.dart", "hash": "0b4a237293e913152ca376cdcfbe752a"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "hash": "5f791a9bba409496763c5d1ff43a9118"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/types.dart", "hash": "83bb9dfd0d336db35e2f8d73c2bdda85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/printers/logfmt_printer.dart", "hash": "1812a211ce0ad9a2385a310cea91bc01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/node.dart", "hash": "2f9772d14db922d3a41fb27f6b6382fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "hash": "7cd08032583ab0a8eca895b2365a4583"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "c165bb259eb18a2dc493a0e7a1d1ebd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/and.dart", "hash": "1e9ed9cdf00b9449d9b72dcd00add4d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/title.dart", "hash": "0cef69b4b620bc5548a97e87b33e7eb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/utilities.dart", "hash": "db8fd891fdcab94313f26c82f3ff2476"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "247fd4320e1e277acc190092bf6d35ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessionmanager2.dart", "hash": "437b5795f5b9bf507b02ed5d44f9f572"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/src/messages.g.dart", "hash": "eb1665fb174bee1200143c40c872b1ff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "7e827f3c407d93dfa01d1c8cac14af80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/basic_lock.dart", "hash": "25057894002e0442750b744411e90b9c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/models/category_model.dart", "hash": "7f1a0ac5bd66db46300ddbf64e6f43d1"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/screens/admin_subscription_requests_screen.dart", "hash": "e6920fb2509c852a28a44183d9aaeb87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/collection_utils.dart", "hash": "add5f0afe8e318e91950e5725be6f333"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/navigation/unified_navigation_system.dart", "hash": "9b7a7fbde66b1ac0d6e637677dcfdb26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/expression.dart", "hash": "79503c7448238b77502c169788e26dbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_error.dart", "hash": "47cb151906114ae0161078bb7968ffc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/stream_transformer_wrapper.dart", "hash": "04d38c19b0c3dba61b730122d76ec4d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/lang.dart", "hash": "7e3f918d0bfe4e1806f1187481eda29c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/nodes.dart", "hash": "8608080cdfc143d462b0f9947dc0d7c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/progress_stream/io_progress_stream.dart", "hash": "6ea89c3bc6b0860bd7c16998d3950c3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptors/log.dart", "hash": "a7730cdfe094a3fdd076fcf5fe39ed65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/hu_messages.dart", "hash": "9601062a07b964b48ea83cc9ede16205"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_import.dart", "hash": "e0a5a25c69f7362ae3d6e493dfc611ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/cartesian_chart.dart", "hash": "65332a226d69a63783d4e31f1900488a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/descendants.dart", "hash": "ffaf08c52f141dda6e8be50b3e46ea50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/SimpleLineIcons.ttf", "hash": "d2285965fe34b05465047401b8595dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_completer.dart", "hash": "b9531c458d313a022930a0842db8201e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/bn_messages.dart", "hash": "60dcf31dc996c50a1ad71e812cb92d95"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/MaterialIcons.ttf", "hash": "8ef52a15e44481b41e7db3c7eaf9bb83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/lib/src/share_plus_linux.dart", "hash": "1e19d73629b4f725c4aed4ea296610d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/scroll_controller.dart", "hash": "f8d39d88c6312434d0a2ddac54dae2c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/release_transformer.dart", "hash": "45a20da2b86984fa0b29030dd190c75d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/tracing_utils.dart", "hash": "a38fd0b83f88d3e2af0df12932f40304"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/models/user_model.dart", "hash": "f31ea20ccec65bcfb8cffbcf7757891f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "3c24303086312d7181ffa10d0521029a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensormanager.dart", "hash": "af29a3ea1a69b956f7915a4cc29d4b89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/token.dart", "hash": "595737cf044c5d483e4615a1b0e1db71"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/widgets_localizations.dart", "hash": "d509a11731c316d5cf31e5a220db0a68"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "b75501071b7ff5d32ddab4c6ea5d2f84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/zip.dart", "hash": "636229be247a1ecd50a669eb2dc73206"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/default_cache_manager.dart", "hash": "8ad6f50f623fbd97c2aa23d86d3c22ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/response/response_stream_handler.dart", "hash": "87061e866d20d4a66d6990c36638681f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/rx.dart", "hash": "f222f3be7d9e176a7d8ba3252825c9f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifileisinuse.dart", "hash": "9f2e86f55227535568e0459c4d51e139"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_signal.dart", "hash": "8596b58c127792783625b4b22a4d023c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/throttle.dart", "hash": "12faaaa2952e6917c271f5dbe9cd6bab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/_io_get_isolate_name.dart", "hash": "295cdb23ca73d107279fd41583eac636"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/sqflite_darwin.dart", "hash": "b1cb91ea7a56d612d5792dbfe439f2d8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "7150d31ecb453ea0d7516ebd2a56ff84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/package_info_plus.dart", "hash": "41af983ad4476c4b4efac50009fe3691"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/guid.dart", "hash": "831a91029162697310005b2ad492c0ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/newline.dart", "hash": "bdd138e5e3c721f9272da59c10d7c5fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessionmanager.dart", "hash": "53ef1e482a9021fe353d68c9f8a1affc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/element.dart", "hash": "23db80d93d6f37b73648e830d1dda0f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/da_messages.dart", "hash": "d8b7bf986a7a310048810965eb89e693"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/enricher/flutter_runtime.dart", "hash": "c9705cca9e18e215220250f01d398c9f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/chat_providers.g.dart", "hash": "f076fae64e5dc3e68e5ed07bd2afbfe9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_splitter.dart", "hash": "698b7b5743b9cfa0aa9d08de156d04b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/task_queue.dart", "hash": "ee34920c892399b0b8ffefec88500c86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechbasestream.dart", "hash": "1632b8b538a5115973c424adb5380d7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/redirect_record.dart", "hash": "91794c215a8aa39b862cfa4c96b9a398"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_thread.dart", "hash": "cc64c3f6a2e71c1e9b36471b5c0ab57a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/version.dart", "hash": "1682a0ccc33d52e194f895d03aff0468"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/arg_utils.dart", "hash": "9812b8e536c69068c0e5f3d3db20c140"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/future.dart", "hash": "443fe4357544b85c13ef051cf37a602f"}, {"path": "/Users/<USER>/mypro/carnow/untranslated_messages.json", "hash": "0307a2c4862283021f3d826f5b7df633"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/plane.dart", "hash": "2a0078c9098cdc6357cbe70ce1642224"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/file_selector_platform_interface.dart", "hash": "eeb75628a0a17d5d8b5dbe0eafc08a29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_converter.dart", "hash": "2d069a48b5e0ffa386474977d2c91c90"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/providers/admin_wallet_providers.g.dart", "hash": "3cda3f0523f8a2ac5ac9e1b9cbde7217"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/LICENSE", "hash": "274f785d87f04e3ff3ed5adc63b7fad0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatadispenser.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_proxy.dart", "hash": "5097d37aee79358be8235ba579f6a34c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/crypt32.g.dart", "hash": "8898ba9f5064edff3e9fbc9889ba9dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/filters/development_filter.dart", "hash": "a925c024faf2d8bc047793e5a39b95d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifiledialog.dart", "hash": "8a251fb90302207f7e9e3f95aca01a72"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/radial_bar_series.dart", "hash": "f8de1c8a4786ba6f05b9824c896f217b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "ceafe3fee68e6597afe301af3cc318c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/src/types/activity_type.dart", "hash": "709682c0dd3d4246f0d0e9e989fc9f30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with.dart", "hash": "f95bd67282cf610843bb37b5784f3eae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/xinput1_4.g.dart", "hash": "08b6eae008bb8359796643eb1a639234"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immnotificationclient.dart", "hash": "300a55743890abdcee4f6f0ac897a3d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationorcondition.dart", "hash": "821dcb1b139f1347a59141ff1fe42766"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_accuracy_status.dart", "hash": "6062adde7b02bc31a016151a95e32516"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/skip.dart", "hash": "be231020db4ff03ccedf0cab8d50d12d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/utils.dart", "hash": "8608f71f077e370ee14d37c711e6580e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/charts.dart", "hash": "664ce9923f62963eff2ab162e125d689"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/http_transport.dart", "hash": "7bb25e76c94856918db28332dfe901ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages_async.g.dart", "hash": "2bd174cad1b04e4cca9ba7ac37905e5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_import.dart", "hash": "afa8ae229bc41c02a6cd9dcbe10a81e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitem.dart", "hash": "6e25bd87f1ef3a06c65b27f722fff88b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "a8513860b3b4c160b57ca6264bc0acf8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/extensions.dart", "hash": "033cc457821088f152cc31f4439f9f0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier.dart", "hash": "b60a2076a519fde0c9162319239b25eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/end_element.dart", "hash": "813218451c1d8dd310e1233bd4ca7a4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/configuration.dart", "hash": "0f86f73c30e6322060a071461bc7c6d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/failure.dart", "hash": "2db6cf613a3f03e05a2c19ca6e14447b"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/screens/seller_terms_screen.dart", "hash": "c6aeebacdbf62d3746a11871c4b37131"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/LICENSE", "hash": "4329bcdd1ac50446158359963f9d3403"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/types.dart", "hash": "3353f65796638e830b18ffdf1a678a3a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/notifications/services/notification_service.dart", "hash": "2ea9b93fef694a170ccdf3dbb45b23f7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b5eb2fd4d6d9a2ec6a861fcebc0793d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-6.1.0/lib/src/messages.g.dart", "hash": "4e8bd1ced5535e8dd6aef24d5a8ce855"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/widget_event_processor.dart", "hash": "7ec9692ea2e8585576938f46ca5d7fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/scrolling_dots_effect.dart", "hash": "17c8e4284f387548c46111ec13fc5ab6"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/screens/add_car_screen.dart", "hash": "66f27d2936c696c6383a78de2cf44f71"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumnetworkconnections.dart", "hash": "4e3b785e94de8470e198d0bda80e23bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/unpack_utf16.dart", "hash": "cfab296797450689ec04e7984e7d80e3"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/history/models/history_model.g.dart", "hash": "f032bd9004a8b9d60f0fe0109d56763f"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/users_provider.g.dart", "hash": "4cb422626bda452fbdea62738540d697"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/future.dart", "hash": "8d3f31cb53177f3f6315575373249597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/dev_utils.dart", "hash": "9a4ee08ca541303a2aee95f83f548ce1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/chat_theme.dart", "hash": "3549bf9b18087cd03979a7804cd9bcc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csv-6.0.0/LICENSE", "hash": "4387180f1653e5266df9e9f028629d82"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/exhaust_map.dart", "hash": "4c61dffec4ef48c5b09f3009e7765657"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/connectivity/connectivity_provider.dart", "hash": "06998c4143aee7d84f6b01edbd33dff8"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/widgets/error_boundary.dart", "hash": "5ffbc45a4e23d75c26160c09de6adf84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclient.dart", "hash": "983f9738507c43e2eee65120e25d0785"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/history/models/history_model.dart", "hash": "f304216242477dca087b3a9e4c82f23f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "ee2f417f35b5caa4a784b24c1bc32026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/range_column_series.dart", "hash": "04e4c74112171ceb22a640c2016b2e72"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/libgtk.dart", "hash": "8f92aefe740231438bdabf80e375164f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/encode.dart", "hash": "4dfc59ebfe657a9fe7ac04ac5b9f70d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/auth_options.dart", "hash": "859fb3f0e6ed9879424415ff38176c50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/io_client_provider.dart", "hash": "b4981708ff3458b729dbb5a76ca55dad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/url_details.dart", "hash": "d0f5b844df029dea3a3cbb79441f2f1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetworkconnection.dart", "hash": "21da671eb92823f3b4c91c47b2e9bac7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_baggage.dart", "hash": "550d483a33f01bc78dc4449cb9ab441a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/models/category_model.g.dart", "hash": "a0b2f32153d5e3fd2e024523ef83fa0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/comdlg32.g.dart", "hash": "cd103a8b0a9727840f3bd8bd985ad677"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/models.dart", "hash": "aa66cbf1b4e7b1fb460df5a9e0327b49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_comm_l1_1_2.g.dart", "hash": "62710fd39bf51f264c7fd8ad1dc7aac5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/et_messages.dart", "hash": "2ee9be812e5bee792113ca6bdbeae008"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_make.dart", "hash": "fff8d6e57f54824612a48a91de0a903f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_consumer.dart", "hash": "987dfee9ed944d2007a00e521d4fbbe4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestospackagedependency.dart", "hash": "30bad556275cf4f7a39d50f698375871"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/noop_hub.dart", "hash": "298964ab486744dfed32efc90536ba98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtogglepattern.dart", "hash": "3796ca959ef2c6e4bfd668640a318ad1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/opengl.dart", "hash": "21baec3598b81f16065716b8ee97c8bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common.dart", "hash": "1ab2b4b439160093cb35c9b0c739bc0b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_span_interface.dart", "hash": "2a1042f3d61c3f3dce359e35aa2e95cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/capture_transformer.dart", "hash": "e82a9b67ba33ae635b9b083ef147fb9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/data_category.dart", "hash": "1089f3ef55be862cb7ce7a73aa8b8809"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "ccdbac117e9349d3ceaa005c645277e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/integrations.dart", "hash": "3e8231b55bd17e31238aa60f861b9304"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "f6345e2a49c93090bc2e068a0a808977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-3.1.0/lib/freezed_annotation.dart", "hash": "c60c0c30ce1c18a3c237c49ad97a1d3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_peer.dart", "hash": "681b70272ec68e757f2394c9e7fa9398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/lib/google_sign_in_android.dart", "hash": "8cdee5a56937f51c66c492b70552cdaf"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/auth_interfaces.dart", "hash": "192fe4208522e11e92182195d304e184"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/location.dart", "hash": "3896d40b189728404ca658a2e9390dd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/cursor.dart", "hash": "5bde4f62a64276d44e1ef4ee3bf194f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/flutter_secure_storage_platform_interface.dart", "hash": "8dac3815609f98dfefa968bc2ea4a408"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessionenumerator.dart", "hash": "e5349492be89ad5eea4187db08b2ad0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/product_model.dart", "hash": "4eb1f64bd367c9547abda3591a44ccbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_accuracy.dart", "hash": "6deecb644bc140e21eff85fa3487c41b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "62b4a318d3ec0d03d3dc78b84cf0458a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/legend.dart", "hash": "1378990f4ee8634a702e83ae5ae3b99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/winrt_helpers.dart", "hash": "8a032ca2b66b8be21ce8368f80406db7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/int_to_hexstring.dart", "hash": "73cb6deeb88fdcc320cf8e089d51531d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/subcategory_model.dart", "hash": "b1e8fb967cd7343f01263828fc6e58c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/group.dart", "hash": "f31a685ec42e95decf8c1937de3a5856"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/geolocator_platform_interface.dart", "hash": "34a0e92ce017d86c6feb973b6a30b64f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/combase.dart", "hash": "90ed8a12c97e362a162da690203df055"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/queue_item.dart", "hash": "937dad14a7958c57948525533b199296"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/admin_recent_actions.dart", "hash": "d9710da62eeb0f1cb86ce5eff95d1003"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/network_connectivity_service.g.dart", "hash": "3748394171b5c13048b9f5d3364883f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptor.dart", "hash": "9c6333c301963385de32595f9442d6ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/wma_indicator.dart", "hash": "c3ab6f094cb3158f6049a03038abe359"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/jumping_dot_painter.dart", "hash": "bae8c3b11a5ac261d9cdfc0bf5ae7909"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/version.g.dart", "hash": "08a0131d87ba3b2535a2de787086a3d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/information_provider.dart", "hash": "e0e6a22d50cab6e16266023c58517b54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_generator-2.6.5/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/subscription_provider.g.dart", "hash": "6bb41b67c7892dcf7f629b39f5936b48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/native_sdk_integration.dart", "hash": "1fe74d311e577fa7e98bbce1efc71f54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/output_event.dart", "hash": "afda74edd611c35dd0a44e3028c7ece8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "f357bc5433a3205fc48000ad8c569c5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/constants.dart", "hash": "808711eba7e3374bd5161036905b982d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immdevice.dart", "hash": "b5e211d1bb1c533a77b5638eede5479f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationproxyfactorymapping.dart", "hash": "7eae5454728dc152e90d36cc6b715544"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/providers/simple_category_provider.dart", "hash": "33a700ed2bfb2fe69e59c269e650f3c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/cast_list.dart", "hash": "87751ee02d315bd2d0c615bbf2803a3d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/wallet_details_card.dart", "hash": "871f17a39518d806a82568be53d6cd30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_neutral.dart", "hash": "bc6418e88ab34ee846e3d4e32408b23f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/models/product_filter_model.freezed.dart", "hash": "ad8802c4cd6ba583f30b78eb9bff2475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/ray.dart", "hash": "5d9bdad87735a99fb4a503c5bee7c7fb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "15ee790ce6b1c0a29d38af8094ad1722"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/FontAwesome5_Regular.ttf", "hash": "1f77739ca9ff2188b539c36f30ffa2be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/regex_utils.dart", "hash": "075058435e254f21ab952c20c9ecaa34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/tracing.dart", "hash": "565c932b397f73250e40947d5428be7f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/widgets/category_card.dart", "hash": "d98a6382b5a3fba4c8266a6936f77ce7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/tag_exception.dart", "hash": "d851ccbe29621b2c3cf5556211b35b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_privacy_options.dart", "hash": "c9f7d7b43f81d290d0a41eb7b0a28c88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "86d7d305c24e6073b89718914fcd3ee0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream.dart", "hash": "809f1f0bbe7ee77e69f003952a5525d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_client.dart", "hash": "d56c8f171b7e19db1659f1350d9ce459"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/scan.dart", "hash": "352139677d0d5e7dbf8941093403250b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_until.dart", "hash": "85fcef4d360ca759563bbfbe7c8d5e8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxfile.dart", "hash": "9147a0ebdb209d3da9ae7cab703710fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/services/chat_service.dart", "hash": "66f76d6a94cb10b0e9d7ce32f3a9398d"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/subscription_response.g.dart", "hash": "5340068d44dd10e6d73e843025ce152b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "438f80a3d5361329aa6113e3409440aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/de_messages.dart", "hash": "c032cb36b7900c73ffd764ab88e9675c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/fixed_extent_scroll_controller.dart", "hash": "dc746ad78bd203740e0d9124909d20e4"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/providers/providers.dart", "hash": "784be5c789cb31625b8d34ced23adb93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/advanced_file_output.dart", "hash": "fbb6c76614692e2915d8fa88317d832e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/models/vehicle_data_models.dart", "hash": "1a41e646af36e87bbe80a81ad6cad48c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/flutter_cache_manager.dart", "hash": "4d339d186836b857e23b70679d7544c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/enums.dart", "hash": "4988e372f39136c7ab470d11011c08a2"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auction/screens/auction_list_screen.dart", "hash": "e2c04dae47d0178dfda35a08bd37a745"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "hash": "85aa53b038be894edc8ed4b952643c56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/google_sign_in.dart", "hash": "831a5b805559c10068c313cadeb0c002"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/fade_widget.dart", "hash": "e1d33f6f03e359759c131d64cf62c84f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/linux/dialog_handler.dart", "hash": "5c44e33ae64d25992b7fbf2390070377"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "5f64d37da991459694bce5c39f474e5f"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/errors/unified_error_handler.dart", "hash": "c8de0462bb5d029f21ae4431fa606ed3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imodalwindow.dart", "hash": "3cafeafccdf2688fe36789f31e671cfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose_family.dart", "hash": "0dd5377006ddfc0b63c193276ef02d43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common_ffi-2.3.6/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "a46ede2164234d7371852e8f57865dd0"}, {"path": "/Users/<USER>/mypro/carnow/lib/l10n/app_en.arb", "hash": "1e8b747aa2093b72ac803a56ba4ac7c4"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/wallet_filters_dialog.dart", "hash": "b23603b75d985e8a329ab8aa3febd71a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/subscription_provider.dart", "hash": "8c4080a354e239d431c0c3976913bd60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iconnectionpoint.dart", "hash": "96c9d801d1879091246f0b107ee4147e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "674ba42fbba2c018f6a1a5efd50ab83e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/shared_preferences_linux.dart", "hash": "492280af61b4bca29e21d28db0c2be1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/choice.dart", "hash": "253b43ba9075c77f9ce5267d91880da6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "1d3f3077faee6bebdc5279446f541502"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/log_output.dart", "hash": "1cc168543c8f88638826f971d68adbae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/constants.dart", "hash": "6f30d0a18f2be5a4a8cf09531ddf8141"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl_helpers.dart", "hash": "c0f563a80ccf76ce9e15cb224b221cc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/utils/utils.dart", "hash": "6c479e0fd2351de96aa7368a1bf8f8ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with.dart", "hash": "46c6500112cb203a46608825824d4d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/each_event.dart", "hash": "91b72e3a75068042bd3b16de99d2c990"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/magnification.g.dart", "hash": "c63a357184bab34ab1e8522808a9cdf9"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/subscription_request.freezed.dart", "hash": "1b3c80c8dff023bbf330e5ff02f83f79"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "f500fac00bc25f66e6f49f5ca6de723a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/animation.dart", "hash": "658dca8f74e5e582925c4dd43afaf2c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/expanding_dots_effect.dart", "hash": "77a4451d7956d21f484d349a1176e02f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "fc0c77cc9957db2d82d3e8d56f8ef9d9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/widgets/app_error_widget.dart", "hash": "d2a4219367ff9bc47aada063491be4ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/hct/cam16.dart", "hash": "7cb71fc5981509df77a8edc6f092f688"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/cached_network_image_provider.dart", "hash": "8a55a3a014cc2ba2dea85787efc98ee4"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/search/providers/search_provider.g.dart", "hash": "33dd9579744b1e5cdeec7525463830c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/lib/src/harmonization.dart", "hash": "ed3310cb36404669e3083a806e16bd59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/utils/platform_dispatcher_wrapper.dart", "hash": "4e38c51061b06f5a0c0a3922f0fd353c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/shared_preferences.dart", "hash": "698b47b813b0194cf3adacff5906a585"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/models/models.dart", "hash": "8a3608c32ef31373460e707ad220237a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "02dabe6a8cd832d69b4864626329ef30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/link.dart", "hash": "c36f00a660d9aa87ebeab8672ccc6b32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/types.dart", "hash": "4a1d1bdbd4e9be4c8af1a6c656730a66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose.dart", "hash": "a57c7d0bb0b0f3ff52fd48c953453bd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/method_channel_package_info.dart", "hash": "5489bd1170add17f6d3bcc248b5ed048"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/idesktopwallpaper.dart", "hash": "28a96a9cad386cca4604fe9b6b0ac250"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/assets/data/transmission_types.json", "hash": "21434644932cb8ed0074ff563a220eaf"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/auth_initialization_service.freezed.dart", "hash": "193cc5b63f7805a3066cd2314c3c8f34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/integration.dart", "hash": "59155766f70d6a46a610372339413183"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiostreamvolume.dart", "hash": "a88c6c3bfbfabb9924b6b0c3475f45b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/slide_painter.dart", "hash": "b9ba10993c139be753f8b82d3ea5f9e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/always_alive.dart", "hash": "ae4469331dace367e6fb978dd7b7737e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "630fe5f86ee37699c534f9c91f21f03c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/notifications/models/notification_model.dart", "hash": "4528447fa631b901e3df57012cecdd94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/cross_file.dart", "hash": "b5c8f4dba868efb80ed69fcd5a7d3f07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/family.dart", "hash": "9dcc50108fd667c7744d5bba6b51e1b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/datetime_axis.dart", "hash": "73740fbd6682b613e1d11403b56486c1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "561522058c0ec0f631fe295300d190e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/dbghelp.g.dart", "hash": "ec2c8a676c3ca12891e9a65ea03458e9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "6d0b38802aff8cbe310e72f1a62750d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/callbacks.dart", "hash": "32961fa7775d5c6b8a12dbf197558a18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_shcore_scaling_l1_1_1.g.dart", "hash": "00bfa437eaf641f6fdf0db2367135a29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/compute/compute_io.dart", "hash": "e990b24e6368a3aa33f21b4695cfcfab"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/constants.dart", "hash": "06637d7006cbce4ac5a29e400cb69d84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay.dart", "hash": "46133866c09984f60ac2731cf9094a27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/observers.dart", "hash": "6a7dc714f0a12990ae998be3d87e3536"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessioncontrol.dart", "hash": "405ff2b0c110ef10a33e496bf7db38a1"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/unified_auth_provider.g.dart", "hash": "959c556323513597c05a8ad5ed833650"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclockadjustment.dart", "hash": "dde1235f5cf091fe6d4a2938399afb4e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "2354ff7691e352dd0fe56e0a46338db9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "9cc3bc193f4ba15acf07b6b1d46d879e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "92e6028556e74c1dc297e332b473f78e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "525e57b6ade38da2132c8ddb0ea78547"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/debounce.dart", "hash": "1aea282ab07e82afe7a564125110e1fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/strings.dart", "hash": "4e96c754178f24bd4f6b2c16e77b3a21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/fa_messages.dart", "hash": "69ed9f3504f9dbb2f884d6941815b85f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/printers/hybrid_printer.dart", "hash": "c7ea8e1b642822fe4d241be13ab160fd"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/utils/image_utils.dart", "hash": "14d36665a3246d5d0b968dd2700c0253"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/localizations.dart", "hash": "5ff2d90544438cc086555a06fe71334b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/trendline/trendline.dart", "hash": "f0b2caf2506a84f83539d710172de1a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ibindctx.dart", "hash": "82c3a291bffe63fdad7d6e4bd5b0a0e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtexteditpattern.dart", "hash": "77fe24649991a149ec3886147da46e40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/jumping_dot_effect.dart", "hash": "08521efd929301df6f2a8816ee634057"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/wtsapi32.g.dart", "hash": "da654b6ae25dd581a1b5f1084d769c91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_encoder.dart", "hash": "85ca5d0ad350ba37b698247c24cb70a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_0.g.dart", "hash": "873f842bb40bf6525129af58dab2e62d"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/utils/type_helpers.dart", "hash": "c7e6110ab66c6d629765310124a5ff42"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_trace_context_header.dart", "hash": "46ee88ea4554412187a82f7d9cc0cb51"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_model.dart", "hash": "08155335d0f8b0e259d28ace9ece3837"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/mime_multipart_transformer.dart", "hash": "531d1d96bce7aa59a6109c02ac538cb0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_device.dart", "hash": "8f49e70a007803aa251ab5fd9c0079f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/char.dart", "hash": "00456c7fcfc11e9ae46af126277652d5"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/orders/screens/my_orders_screen.dart", "hash": "1ff9b465884d589c779980236fff2b60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_log_batcher.dart", "hash": "1360c192645f82884ccf2668227735a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishelllinkdatalist.dart", "hash": "a82741847c5177c47adfd428a1583744"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/models/position.dart", "hash": "de40378f7ed011561b6ec6bbe2b5ed63"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/l10n/generated_material_localizations.dart", "hash": "d77b409cecb2f31670f4057524b4d5f2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "177fda15fc10ed4219e7a5573576cd96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/permission_definitions_not_found_exception.dart", "hash": "37811c1d6ef37aade25e3c631bfa230e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/util.dart", "hash": "c6cba4ae8b80445cb220fa9a09bf9378"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/internal/reference.dart", "hash": "f25bbc73708cc35ac55836cbea772849"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/parser.dart", "hash": "505fb0828e4fe58e1a49ddab272d7b71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_web.dart", "hash": "71b9fd89c14d2a8a39275d81a2500c5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/recorder.dart", "hash": "c7e69d0404e649e6c56d93c4bce71916"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/search/screens/search_results_screen.dart", "hash": "5eea5a988f494ad4acf1d91f63087909"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_not_null.dart", "hash": "929b5628541e8ab826e753c9fe90cd30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/bar_series.dart", "hash": "a683628d86d381bd373055f8891b7526"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/src/messages.g.dart", "hash": "5b9ec782f9739612abc43813e94f2545"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersist.dart", "hash": "a1f73c43919636da8b8f9a657ca8cc14"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gsettings_memory_backend.dart", "hash": "1813a66c9593ac1c9b37e2ecda338c6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_stat.dart", "hash": "1b430815bdc7bab3a240f27e745f8977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_log.dart", "hash": "1f550f36add45879b0f2e5b6ad300005"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/flutter_exception_event_processor.dart", "hash": "27e5c78af59299c7c2aca680dac0f967"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumspellingerror.dart", "hash": "4454497beed7948ccb9d6987d52ff3fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/value_utils.dart", "hash": "91921fef1791885b747a338372bfdede"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/wevtapi.g.dart", "hash": "4fd8d39dff594e013e042c2896cb0bf0"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/carnow_transaction.freezed.dart", "hash": "26115496612def57bc542548c28bd6e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/value_provider.dart", "hash": "d5a669dc5155cedc975db1022a570128"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtransformpattern.dart", "hash": "ff5c40ddc1501e3be7aa7efd4a269f04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document.dart", "hash": "0f90625420cd7d017be4426f0bdaf0e1"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_makes_model.g.dart", "hash": "d73078581a4347d3d55e5cd2a0679f9b"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/carnow_user.freezed.dart", "hash": "a5cf23d650f4734edc513777b0f5625b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/listenable_selector.dart", "hash": "275b17a5132c35fbdb46851dd0780163"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/background_transformer.dart", "hash": "c3ab437aa0b03081adbfcdff7755b358"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/carnow_user.dart", "hash": "435587113135aaa7fbe9a4c70a574eec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "8b20b418804c1d6e59afdfcae6e84728"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/sentry_navigator_observer.dart", "hash": "2ace264f00f155f9f5ef8b8f4dc0041e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/scheduler.dart", "hash": "2f63e230c7e48a0b125c48a9f0b4da9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_path_l1_1_0.g.dart", "hash": "42efc7a615bdc348ad78098c1cdb79b7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/cupertino_localizations.dart", "hash": "4b64862d7017b3b2e105435437ab5d88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/screens/login_required_screen.dart", "hash": "159734e620a0fbfde201d2934cfa7012"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemconfigurerefresher.dart", "hash": "0502dbd75b5b023cd08bf81003a77889"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/interactive_tooltip.dart", "hash": "df1c6d37fd3eda86ae69e58636410bbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_subscription.dart", "hash": "e2d2090c2a39f7902893e64150fe82b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/sdk_integration.dart", "hash": "68df481c4110a101e1a5baafeb9a627f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system.dart", "hash": "a0432b1db3ddabe8c3edb6f542c9ef48"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "9b98b196040f00fd2fbaf5f7a2309e6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumvariant.dart", "hash": "ad6fa6bf1dadc6e07c4c080c69abde6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/services_impl.dart", "hash": "a6d82f072fbaf76b1276861d20c1b788"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextchildpattern.dart", "hash": "3d63c4213a898f6e0eb52cb39fa282ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/zh_cn_messages.dart", "hash": "df5104cc83ec9ed2205b381c4e3fbd4c"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/carnow_wallet.dart", "hash": "6590ca31e1e577744de82c84da4a76e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/json_annotation.dart", "hash": "532a272d043c3dccd91b63d1b428dac9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/list_converter.dart", "hash": "5f5f3a1074f40b8fc37c2b3ba5ec0432"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_app.dart", "hash": "446573d8990f4ff9fe7733c9dad417e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/exceptions.dart", "hash": "0400c53ca2e9230b51a6f361146dee28"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/recommendations/providers/recommendations_provider.g.dart", "hash": "5edcf3a3858f3150f5a3380100191e51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/image_picker_platform_interface.dart", "hash": "b152cc1792a66ac4574b7f54d8e2c374"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/navigation/unified_app_bar.dart", "hash": "e8581fe131cf527712057cb643b0afd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/performance/startup_optimizer.dart", "hash": "c213c074cb41877034a7cb734635d938"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_maps-0.10.13/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector4.dart", "hash": "7d33539b36e15268e2f05b15a9f5e887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/auth_messages.dart", "hash": "f36296a5403e8c6265b437c034411f50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jlong.dart", "hash": "3a2ed1795b55d3190a8e284d8dcd2f78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio_exception.dart", "hash": "2747964c64fe300f15d15123727cbcf6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils.dart", "hash": "a50b339d8050df9fa1f095bb939c66a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/treemap_theme.dart", "hash": "e38450165856054a474b48fdf25df1cf"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_generation.dart", "hash": "f5e5993fa96ac8c9ebe7f98c65c555a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/performance_collector.dart", "hash": "1500e860f633a60fdac20cc05d034122"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider.dart", "hash": "d5b1d01f918c452585a990bba4c2b919"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/mutator.dart", "hash": "e105e8d3303975f4db202ed32d9aa4c7"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auction/providers/auction_provider.dart", "hash": "ecc021606cf7d9208183b5458ce305c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/foundation.dart", "hash": "f594087d1804ddc538f758c0059eb6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/transformation_controller.dart", "hash": "4d93524921f3e77be65de99b3eca9ed4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/char_code.dart", "hash": "4fb96b9e2073cadc554a25b36f55e6dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/transaction.dart", "hash": "95701ee376845a2050d29814b7acc7a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/winscard.g.dart", "hash": "f0ffece0b01158318dbca4d043da78b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/behaviors/crosshair.dart", "hash": "420a09ddd43cff03ad68130dbc722695"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/profiling.dart", "hash": "81f2b7927c91121715a84fe950cceafa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/localizations/global_localizations.dart", "hash": "358416b83855424a3433e2cf6a730c43"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/providers/account_provider.g.dart", "hash": "e34f52a0cbd3fa00f721ba800a38bea3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_web_image_io.dart", "hash": "e88b0574946e5926fde7dd4de1ef3b0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/client_report_transport.dart", "hash": "9b524c185849dfffe570f36e3d2fddc1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "a89f6417642d57961ee87743be4a6a2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_while_inclusive.dart", "hash": "389552e6852c3214ca6857ddadb7cd0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ca_messages.dart", "hash": "c681ed2471dd12d4d76912530c825c70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_line100_series.dart", "hash": "c9b7a54d0dbc526f3adbb4fa35fbcfb3"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/dio_provider.dart", "hash": "f878c0e5d7dde6f77f6774812b74f5e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/core.dart", "hash": "0ca14634f99a2782c9127c87d0f792b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "hash": "9fdd6505b69298204e1355005daa660f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_item_model.dart", "hash": "e91c59dbd0ea80c9aa41ac26ce7bad8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/result.dart", "hash": "fbca1545a7230f0ea39d7884a1722475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/with_latest_from.dart", "hash": "eab456316eb52f6f668d1dd2800c1085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/namespace.dart", "hash": "d7259aeee1602df30d051e8fc0523d91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/printers/simple_printer.dart", "hash": "178f62efb676bb0f4293df1f3f7beef7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/LICENSE", "hash": "d229da563da18fe5d58cd95a6467d584"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/method_invocation.dart", "hash": "42953db2cc3f9fd0cdec4c95c896905e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/smooth_page_indicator.dart", "hash": "c14111d4ae72e398d7ecb4fa4916adec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/src/messages.g.dart", "hash": "814815839a4b6d2924a5a8661780b0cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement7.dart", "hash": "f05adccad12249a4f175efc9b8abfb37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/transport.dart", "hash": "95478c24cc93bc3fe1a16eeee30e23ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_format.dart", "hash": "6cad3d78b208ef8a929f29c2628224e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_group.dart", "hash": "d16df8af6c029bc5e12bedcb2d9ed464"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_bar_series.dart", "hash": "e03321f4099f333d1f0c4a0da7be5632"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/services/notifications_api_service.dart", "hash": "c31caa247b2fd3d833c6f2722f6a1598"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/run_event_processors.dart", "hash": "fb0d23680789944c864d1e0218808ffa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ja_messages.dart", "hash": "56e33d5904229ddbc511e22bd60ca93d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/parser.dart", "hash": "7aac958977c79edf01e6ad44a726b52b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemobjectaccess.dart", "hash": "3ce0f30d7026f6462449617764734437"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_area100_series.dart", "hash": "b27f280ab656d30d0c3f174766b54788"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/lib/src/windows_version_helper.dart", "hash": "1f4f05a739274cdeb88c110bc6561ae8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/empty_points.dart", "hash": "6854c253df03b4791df243dc2409a59d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/tree_sliver_controller.dart", "hash": "1175907b6f22b886153f577f38a18662"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/cancelable_operation.dart", "hash": "57ef1f2eff2168c2e2ba1c3e4e60e05a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/numeric_axis.dart", "hash": "87c42a3c21dd3de909dcf1e68fa6183d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme.dart", "hash": "81cd0b075cd4d742a83ed1a6f4f801f7"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/providers/vehicle_list_providers.g.dart", "hash": "f477d9d15bb8fa6cefca2f11674dd2f1"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/models/product_filter_model.dart", "hash": "2cd192222484fc23f4b3950405aaa854"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/subject.dart", "hash": "4b93fc559e6626b4d42e924b10c58678"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "166147b7bee5919995e69f8ca3e69d17"}, {"path": "/Users/<USER>/mypro/carnow/lib/l10n/app_localizations_ar.dart", "hash": "c08f47aca5c1277f1489c31f759a2ba8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_id.dart", "hash": "2b69e2643904b49c3f7bb81294d29a1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/theme_widget.dart", "hash": "7cb9957da9b661af1db0ffdb90eb01b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "2610f7ca2c31b37ad050671aafbccdd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/scrolling_dots_painter.dart", "hash": "ca1501d030dae839b3636aa73ed1768d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_trim.freezed.dart", "hash": "a74d9d5fed9307e936571c3c3241fd1b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/do.dart", "hash": "96594345ee8d89e2fd23dbca09121153"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatatables2.dart", "hash": "f1f175eff474684786b1b6980f386aca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/interactions/selection.dart", "hash": "188cd5aced4f379678728c47a790da06"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/repositories/vehicle_generation_repository.dart", "hash": "4fb6d6e37b13fde98bf13cc76026a283"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "67241b28b6ab2188280fb614f1607b2d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/screens/complete_profile_required_screen.dart", "hash": "22d26e165abd6a196f7d2b60efbcb81a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/factory.dart", "hash": "d333e9ae8fc3047e0c6c084fe8ac3c5e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "047052ee1e98c394dd79f1ddf5983b4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_enum.dart", "hash": "4817a73df1c313cf6a6eb86774e7fc99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationvirtualizeditempattern.dart", "hash": "34ac34257c6ee30da8c0b6de4d0a5444"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/shared/types.dart", "hash": "7e327134a49991d7ba65bbfe46bb8f4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/on_error_integration.dart", "hash": "3f23725020a52638916401a176b29e60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/native_app_start.dart", "hash": "b3150fb106b8edb5700fb86430b414ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix3.dart", "hash": "64b9fc5ffdc9f1ba801b6ccf099347b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/compute/compute.dart", "hash": "12b8cbac25c7ad95ce53c2f8869a1b5d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/cart/screens/cart_screen.dart", "hash": "dd0ba69d31f6e5526616906c8fe80352"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/possessive.dart", "hash": "08e17247b131fb75466c336e9a11fcfe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_ext.dart", "hash": "f60f5b36258d9f4596034c1b3655c501"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "04f538d5fc784c89c867253889767be4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/column_series.dart", "hash": "fd05f755a79ec871d9cc5d35a8613dee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/io_client.dart", "hash": "278e31e5169963c0c3ea3ee9b4bb4469"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jinteger.dart", "hash": "cdbfd5d228d7e0abbd09b48fb8a5ec3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/limited.dart", "hash": "bfc3692929b6ffa40605428f3cc70e86"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/providers/category_provider.dart", "hash": "5610194aa15210f7324a782665738c64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemhiperfenum.dart", "hash": "adebe1537e162fcbe4404ab29e94fef9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "c2dcf2bcdc85d007f9729621d13cccf4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "b269f9d6378b540b7d581db466ad98d3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/feedback/sentry_feedback_options.dart", "hash": "01e2f24692e29d366eb7f7cd93c68e43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_file_system_io.dart", "hash": "35c142ea243059f941a4a896a8e053ae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "d5bcdae8bba4c191294311428a954783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object_tree.dart", "hash": "eedac0b4fc9b2865aae62ba790f0e26a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "8807672a31b470f53c5fcc2b36dcf509"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/keep_alive.dart", "hash": "aa45c4388f8947347a8563ba406dc2f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/axis.dart", "hash": "54d558e9a930c48025c00074f91e1d5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/release_sink.dart", "hash": "e2f7d6fbeb362176a24cb422a6dd8193"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry_when.dart", "hash": "add862853473647f3bae9dee0b365857"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_many.dart", "hash": "1f04f05279660e26d85fff2f5dfec4c3"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/widgets/error_display.dart", "hash": "be2c5f45288f64310566cadf7670a710"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/log_filter.dart", "hash": "32581c4e1ac594b374549efd0b5f46c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/LICENSE", "hash": "906742df8afd59744edfde69b6b6f7e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file.dart", "hash": "d9343422c8a6829bd05698de67232591"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/composite_subscription.dart", "hash": "76cc6c2b845bff11813d968688280b36"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "112daf1e5c2a46f4b457e3b76cf569ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/proxy_provider_listenable.dart", "hash": "2e59aadb17c005953c2accd529aced98"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "5c5a8f737a2cec1d969f4a9f8dc80a8d"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/token_storage_migration.dart", "hash": "5d33f90aae4705a754cd802ba29dc080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/shell32.g.dart", "hash": "c1210af8f1663dc5959f1ec44acaa5a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/environment/_web_environment_variables.dart", "hash": "fa1a65a37805aef2fbd0b9a17365cd0f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/screens/profile_completion_screen.dart", "hash": "4d50bd8e2d550b2c7d364416a56a4d7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_closer.dart", "hash": "cbd0196f25d2f055736beb3052a00c19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/scheduled_recorder.dart", "hash": "b466f2ad7e61c4d340140b3a69f19cff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "ebd06d8f4cce7c59735a2ba28d6dba97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_buffer.dart", "hash": "22acb270c1bb267ee16b3d64a3faa825"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-6.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/assets/data/valvetrain_designs.json", "hash": "2ebc8cae64e448661e8f9eb359fc8078"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/search/providers/search_provider.dart", "hash": "0c6827246a3e26dcdf1c0dfc87f8a22c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "1e0ea989110b1544dbaf1fdf3d9864cc"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/clean_products_provider.dart", "hash": "352392c05e84912e449d96d232b71459"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "58490e33e6e99c4e4e313491a36cf23f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/recommendations/services/recommendation_service.dart", "hash": "bf832a9dc473f5bc30920e5b5004ae9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/hct/viewing_conditions.dart", "hash": "31e9eaae0175cc8b4184df6c89b454ca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "f5dab330de9938d8ad99263892810f3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/pyramid_chart.dart", "hash": "1927cad9820f431eb9efdc787ec6bf05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/not.dart", "hash": "d4acdced936c4825eed27ed61fc28660"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/legal/screens/terms_conditions_screen.dart", "hash": "874634147cf8f786b0dc825a06fb4aa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/utils/utils.dart", "hash": "04f2a3236f9f0080d5571041a0cf3567"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/debounced.dart", "hash": "27da8794de99894c0eef51652acafde4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersistmemory.dart", "hash": "cdc3ed60fc9f8d6e2fd72afef2012bda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/multi_lock.dart", "hash": "2ac6fe0e9a4d7b15855dabd7468cc320"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformer.dart", "hash": "49dba21de16234aaed28f8fd898543a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/repeat.dart", "hash": "57ef315bc7f35da7e489915ef8572118"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitem2.dart", "hash": "908da18a3fee181ac432b85d7097e5f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_method_response.dart", "hash": "f29d1458f73f015dabefc27f98181f05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/grammar.dart", "hash": "467b2b8993363b1b27f034f6c1cca476"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/list_to_blob.dart", "hash": "56d7144236503f311a7d9a966eaf2fbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/LICENSE", "hash": "9741c346eef56131163e13b9db1241b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/extensions/extensions.dart", "hash": "351826c32455bc62ed885311dd1a1404"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/ignore_elements.dart", "hash": "908b86c4378330e5b303026c8c3e29aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellchecker2.dart", "hash": "03b20b9fede21601f0b3d0f7ef4ce25f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/LICENSE", "hash": "f0c5f055cb4c3d651ff4c27f4967ecea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/src/token_types.dart", "hash": "d5cb79ec9c68523d6c9ded512385f98d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwinhttprequest.dart", "hash": "e9c0088ee89cdab9346358a1ab7d4f18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_properties.dart", "hash": "953396d57b69e0e889d9dfcc4f7fdabe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "3f3682db58f83007aada4d5c36376b90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/failure_joiner.dart", "hash": "322037160bbd0d8f16bd4e77abfc61f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_stack_frame.dart", "hash": "8be3fd9b87bc793a24386f1b1e574656"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_android.dart", "hash": "23149dd1dabb201f41ccacb25e322741"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader5.dart", "hash": "85574281bf7d7bee9722a21e092b4be0"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_request_model.freezed.dart", "hash": "0e9412387f46ce7996a55fc539f4b68f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_browser.dart", "hash": "5006e605f685c6a899f7e4c063d08596"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/scheduler.dart", "hash": "1ac1f41185397129f7ea925130f188f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/checked_helpers.dart", "hash": "c668a1bfe65f14c115a3294ac6502dca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-3.1.0/lib/freezed_annotation.g.dart", "hash": "6069e6fafc271c3a40899375e25cb5de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/access_aware_map.dart", "hash": "984a2f92d3fef0c24e62a3c37f519793"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/replay_quality.dart", "hash": "6db02cb60daa5c2562cff867c4f7945e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/utils.dart", "hash": "2d3b2846d7071fb93d36485c261040ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/id_messages.dart", "hash": "6e5169b25eda9d7eb9509b58a0bdc51f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/default_method_channel_platform.dart", "hash": "c7c117f8683bd3335fc56fb4e706d271"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/group_by.dart", "hash": "6c3232594edbc47bd6ec36d04c194a9a"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/Foundation.ttf", "hash": "e20945d7c929279ef7a6f1db184a4470"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/c/utils.dart", "hash": "37b4afad4aeeb7df464d8d1f4ea000b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadataassemblyimport.dart", "hash": "dddc2f13e029b11ddffa36413341f1b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/geolocator_platform_interface.dart", "hash": "f97f27b271982baf14111fc68c555151"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/errors/unified_error_handler.g.dart", "hash": "6f2574e182a10cc1a73644daca828da0"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/subcategory_model.freezed.dart", "hash": "8f0082fcec6942a4927d8f22a7609cfd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "84e117adf104c68b0d8d94031212b328"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/geoclue.dart", "hash": "037a6011ed68a8f92864add8f3f813a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/constants.dart", "hash": "9a463f361999508124d9da4853b1ba5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/propertykey.dart", "hash": "241ccb24efad22e002bdfe778f96b46c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iinitializewithwindow.dart", "hash": "0748bf03bcf37edd1d571959e45a5cc0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-3.0.0/lib/google_sign_in_platform_interface.dart", "hash": "56af112e9a7885ad708f5a47945668f3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "59475498db21e2333db54d6478af7c94"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_makes_model.freezed.dart", "hash": "887c793e4e086f3b14c7a1a71ca8c5ab"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationmultipleviewpattern.dart", "hash": "509de531546dd357cb81de8c9e42312d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "a7ca596d88ce54ac52360d6988d7c9c8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrangearray.dart", "hash": "c81713fc58f35111f30b5ef09b79cef5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/number_symbols.dart", "hash": "aac4f5ac61e2386363583c54f2e49a7c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "075310a7fe661b71e9a583aab7ed4869"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/history/models/history_model.freezed.dart", "hash": "8df6c7f88a6dd49dea5edf725dbc8540"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationnotcondition.dart", "hash": "1fec236f729d3217c13d42295fe3faf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/file_picker_macos.dart", "hash": "0f63835de9f786a5a2e46a3067bb963d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/LICENSE", "hash": "f29a26e7da1ef36001a9e33e4871938f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/screens/unified_profile_screen.dart", "hash": "460ed9a8db3bfaf59a4b390e5c19a1aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/repeating.dart", "hash": "282aa0046bbbfcbc30050f7fab282778"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "6dbd6092d46d1cfb37491463002e960e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-3.1.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/listenable.dart", "hash": "bebc62e367d13126838a2a62689f652d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "dc552952c58db02409090792aeebbdd8"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/subscription_response.freezed.dart", "hash": "4fa29ddc7a2e5cff4697c9c1e64e552c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/model_year.g.dart", "hash": "4091deed9430c747a30675eb1357a9ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gsettings_keyfile_backend.dart", "hash": "3d92bfafd77a5d827f0a185ca6390de2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/accept.dart", "hash": "740f17823564c3c7eca15bca5c110e17"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/recommendations/widgets/recommendation_card.dart", "hash": "59b905eefad38da32c768496be6b673c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/printers/pretty_printer.dart", "hash": "bf2bc3af52875d3e5715ed2dff220c07"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "3f9362642d37e0d97860181e8a1dd598"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/http_transport_request_handler.dart", "hash": "0f25aea76fc3fb4fd1d75afc30f45aa9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/swap_painter.dart", "hash": "39adff8f2891a50b780f166c9b036943"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/subscription_request.dart", "hash": "7f13ca7c4ec9990246f015b0e348d9e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationselectionpattern.dart", "hash": "2ee116ca87b7e1c461de1462c3442ec6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/transformers.dart", "hash": "5d2971806de340d9e970e21af445505b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/lazy_stream.dart", "hash": "1649ee82914f6ad1fd46de466dc03378"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name_matcher.dart", "hash": "5c4dc37f36fc78823f785b92b944560d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/models/user_model.g.dart", "hash": "ddbb6e83bea19ac6e207f6a167f3bae9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/assistview_theme.dart", "hash": "f20cfaa6cf0b54f55e4dbcd9a06f1bbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/shared_preferences_android.dart", "hash": "30bffdef523e68fbb858483fd4340392"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/screenshot.dart", "hash": "704a498b6407c57530901678256cea04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/annotator.dart", "hash": "2678ef31818710bda6610b84fc25d915"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/uppercase.dart", "hash": "7061b91c27425c907020fe54e569b9db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifilesavedialog.dart", "hash": "de786aad9aba3c37b121a1f0238119a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/widgets/unified_app_bar.dart", "hash": "8981cd05713ccfe4ee7bc9d0900c605c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_source.dart", "hash": "da5faa2d91b7029347d1a39bc0060cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/quaternion.dart", "hash": "82a52b42ca10c86b0f48afea0cbe9ac7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/rxdart.dart", "hash": "6d2dba952020d690bfc0aaff3adbcd65"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "9ec81b597c30280806033b70e953b14c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/picked_file.dart", "hash": "90a070dfee5777a4bca169be4bda3bb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_monochrome.dart", "hash": "5f22b5b8aa08733c616d6f636a05f5f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "f26f519ea124441ec71b37df7cfa1ee9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/named.dart", "hash": "c5f3b8d4c2e6f53c5fcbdde1e0f03f4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/run_guarded.dart", "hash": "ddefd207562d7e33dc44d433e0848e1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/pl_messages.dart", "hash": "426788530891537f946ce9a3a9913527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwebauthenticationcoremanagerinterop.dart", "hash": "aef722a64f462b84d30dad6278040fb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50/lib/src/messages.g.dart", "hash": "2f0a0bc8c94bf7cd746c5fa5d47040ee"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/orders/models/unified_order_model.freezed.dart", "hash": "3d3bf9f628e4e3db31058ff97e28a826"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "f996ce49eab57718350b84e11ea3192d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/color_scheme.dart", "hash": "83ad217e0a397b80acdc4d40087ce806"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_method_call.dart", "hash": "da6f500c03c005a207d38c1daf24b00a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/LICENSE", "hash": "caaff9711566c556297a1c1be2f86424"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/android_platform_exception_event_processor.dart", "hash": "340d4a9294ab3c453fb56d2e2ac34dbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/parser.dart", "hash": "13255a7d5a3edaa79e467810f1290f48"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "3d5ecec2ff4236c99de1acef7a20a152"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "8f1d7bd8be5bc9a71d3131f835abdb80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/sequence.dart", "hash": "0b1a431f52b54788ec3e9b6da7d87909"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "0ae47d8943764c9c7d362c57d6227526"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/shared/annotations.dart", "hash": "9a469ff3de60c96cf2f9b0523b651782"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/models/user_model.freezed.dart", "hash": "2cf780ee5cd9d6d87a51f9eff806b47d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/result.dart", "hash": "08e7cd384cfc0214e088945638139ce9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/base.dart", "hash": "a4d3fffe230049cb1b9e13688009317b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/bidi_formatter.dart", "hash": "5c81dd07124ccc849c310595d9cfe5be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/draggable_scrollable_controller.dart", "hash": "f9107de0675d28a9458ca8af1dcdb341"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/app/app_initialization.dart", "hash": "05923b98c4914f429fc2d1d25c9439a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/batch.dart", "hash": "d88008fc349dd84def0654263c6d16be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader7.dart", "hash": "a60dd773b7d69b347521fb64257f9397"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/screens/seller_dashboard_screen.dart", "hash": "44f80bba271003428753e50ca4a3673f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechvoicestatus.dart", "hash": "5164e5af0ccfe7dbe777bb588e91c937"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/auth_models.g.dart", "hash": "07f3e34a2a0fccbb02abc36225f84325"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/th_messages.dart", "hash": "2c12f17c825caec63da5c9490b2ab38f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/sparkline/utils/enum.dart", "hash": "bd2087833c55d06feb3badd026c137a0"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/google_auth_service.g.dart", "hash": "3c4c74eed722ceedc4170c2f43b5eb0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/error_and_stacktrace.dart", "hash": "491a33282b614f40bd0fbd3f3b3d45f1"}, {"path": "/Users/<USER>/mypro/carnow/assets/data/trailer_types.json", "hash": "807ff6d1208d1267ba822ba5bff0f400"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/java/android_replay_recorder.dart", "hash": "15a29e3f2adcf70a1d8b6ef0207ec585"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/providers/location_provider.dart", "hash": "cb3ccc4febae0173d87c6e84861491ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib/app_links_method_channel.dart", "hash": "ad6cac082133f54612e91cc36888b468"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/models/product_model.freezed.dart", "hash": "b8a27ec26cdecc2b9a2b5924c26bdde4"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/FontAwesome5_Brands.ttf", "hash": "3b89dd103490708d19a95adcae52210e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/rate_limit.dart", "hash": "aa045a506e1ec79eaf74bc026e62ffc4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/parser_pattern.dart", "hash": "79a5f25a1a9d4aa4689bf37171e1b615"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/misc.dart", "hash": "7e7b95f530e79091209a2a34b61fb085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/file_system_transport.dart", "hash": "dec2c1d31b1408322809e5ccaedeb576"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/async_memoizer.dart", "hash": "abcb2d6facc18b2af070cb86cbb1c764"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/location_service.dart", "hash": "6d7dbfe95af9968f926acb2b70908c3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/extensions/integer_extensions.dart", "hash": "73ca94dbbbfdf54a4125b937afb164d9"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/providers/admin_management_provider.g.dart", "hash": "1bbed401d9b495baf1273732aae84185"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/interactions/behavior.dart", "hash": "910bb4d4e87d123733b014510e73ee7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/invalid_permission_exception.dart", "hash": "7837827426418dcd8970e0032a918ccf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_manager.dart", "hash": "b188e0026dde1c7ef925b5efb80450ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/nio/jbuffer.dart", "hash": "ad83d65961678b8774174a7c65ed1cf6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "1c7764fa08241a44711301c74fb658df"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/sr_messages.dart", "hash": "9edf92861d576bdf045c42ebe808c558"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemrefresher.dart", "hash": "5026f3bc8f63a10ffb208a35e304f40c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/theme/app_colors.dart", "hash": "9679789156bf66557cfa3a069766286b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/colors.dart", "hash": "9cd03844c4e859875c10c9708556a0db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-85.0.0/LICENSE", "hash": "fde2b1b7d744e3606529be50acb7fded"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/widgets/account_list_tile.dart", "hash": "69ada5d1ef20792f1a8ddd42317e1424"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/fonts/Cairo/Cairo-300.ttf", "hash": "fa2b785b431fa365329cfbf3955f1b88"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetworklistmanagerevents.dart", "hash": "cb223d2445f2caf7a2617e25ca761ff4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/typed_stream_transformer.dart", "hash": "991902b33f1d81c417b707a41341ed59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/sentry_display.dart", "hash": "977935bfb8c2608b2fce8c49f217da3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/chunked_stream_reader.dart", "hash": "14acd577a81cd5aa871c66f430b95d97"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "84589f907e3e4d8fc72e5c786a0530f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.5/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/enums.dart", "hash": "1c71712af9ddaeb93ab542740d6235fa"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/services/notifications_api_service.g.dart", "hash": "a450354bc02468850cd8cf7c5ec88dd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/spotlight_http_transport.dart", "hash": "4e4ea0cdb0b9007710bff1003b365268"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_profile_model.dart", "hash": "b9b5bd31d4043860af31ff808bbd9930"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+24/lib/src/messages.g.dart", "hash": "f1c7d23cd6db9504510e67e2957b4aef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_traces_sampler.dart", "hash": "6c40afcf92017805fed96e87e40e55b1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "81036c1ed2827ac1db9fee5a900f568d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "e5ebffb07608ee2f93a7aa4c23848564"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cd7a7fd807697152dfdaeb3109e4f4f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "b6e992b1127f8376358e27027ea7a2ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50/lib/local_auth_android.dart", "hash": "d1014fc9427deb3355db0b126083ed2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationcacherequest.dart", "hash": "15ee18405ccd7752c3035b2f3b86e49f"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/google_auth_interface_v7.dart", "hash": "acfc9c97ba5373d06df06bd4c5614171"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/web_options.dart", "hash": "7dff3a0a1b5652f08f2267907c79844e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "7b0e6dd1794be4b575ecf8af6475f0e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/my_messages.dart", "hash": "ab558fa0781e42f7978f12b30bc0653e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/event_attribute.dart", "hash": "304fc982848b57cf13da0ec511f05ed9"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/search/screens/search_screen.dart", "hash": "b6769dc32aea6298236e61af5869814c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_uuid.dart", "hash": "c9efc107e2b16a48d4e132bfcc679af4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gvariant_database.dart", "hash": "2d9f64f2e82cf015ff889b26dc9157f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-6.0.0/lib/method_channel/method_channel_share.dart", "hash": "058da781e6641dc3b348f46d6742dea8"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/models/product_model.dart", "hash": "e4a68b7bf9f0ce2790df17899a30ccc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatatables.dart", "hash": "02b96169889bac260344fa44343235e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/activity_missing_exception.dart", "hash": "79443d9def8c2f6b6acfc2816be9c6af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/idispatch.dart", "hash": "8ef246eaf180b7621f716282e295c950"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "a9e3af96f170745db1c281777cb6bda9"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/carnow_wallet.g.dart", "hash": "c8fcff8dcfbc81c85caf16d61ae01d70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_item_model.freezed.dart", "hash": "a062d74deaf490469b25de95c7d22b29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement9.dart", "hash": "13e53604d98eb0a2fbd871588ec8b357"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/cache.dart", "hash": "e0cbefa359309715e5101bce98eb65e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_darwin.dart", "hash": "644e5e32abaad61eb192128f412424ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/options.dart", "hash": "e64d63aabc0975a7e9fdb384598c2f8f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/repositories/garage_repository.dart", "hash": "5a9b99f380f52e420e5e571481c3e307"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_zip.dart", "hash": "1dac993c7444b99a17f2dcf45acaca97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/client_reports/client_report.dart", "hash": "c4313121619d4ebb0924cb542fe020ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "2d638931b01747be8315be89cd473caa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/util/jiterator.dart", "hash": "20e865436d949c0cf4d93893818b2950"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "1f131d7f971396d52ce5fe78ae6a8a83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iunknown.dart", "hash": "314ca45445509ac0635a48d2dacca294"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/carousel_controller.dart", "hash": "8dc8d76a748918b4e0e7a20fd4abb006"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/datagrid_theme.dart", "hash": "b16aacfdd3928390a6082c3f36c6cc06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_event.dart", "hash": "14f900dd1268ee4c70ae93d452e9a21d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_specification.freezed.dart", "hash": "9f6454e4b6736ec9a09455115aeb3f31"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/repositories/vehicle_make_repository.dart", "hash": "71a4a994faf3715834b2df334e1a4f36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/nio/nio.dart", "hash": "64a1d779989b9faa687c238e76d57530"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/manager.dart", "hash": "db1b9ef22ea1568a450ed012e3f62e1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/analyzer_plugin-0.13.4/LICENSE", "hash": "e2855a4f7db421253212d56a277c54a7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "7217dd37b49bab8e0319d4fb26d14d8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50/lib/src/auth_messages_android.dart", "hash": "cd9db4ac4b35f0a15d74d1c6ce32a107"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/streams.dart", "hash": "25a929555febc01ae405a334b5ab9ce1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/file_version_info.dart", "hash": "6b943be06664ea45e0cac8c8178920b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/ios_options.dart", "hash": "704d7f872888ec6e9697123a180fd95d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jclass.dart", "hash": "b554604127c761fae7b923709daf4117"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/errors.dart", "hash": "8b0b489cb15690ca7aa27a82947d2270"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/typed.dart", "hash": "35c9371cbb421753e99a2ca329107309"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "6cf1ca324535366e2ea214049ffc9918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/location_service_disabled_exception.dart", "hash": "190314300b619a2f73f112d1cfb29f76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/worm_painter.dart", "hash": "f7ddb89d543ca8b43e3af9eaa24ba4fb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/screens/admin_financial_dashboard_screen.dart", "hash": "c02079f477ab224f187cf04bc6c28358"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/Ionicons.ttf", "hash": "b3263095df30cb7db78c613e73f9499a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/screens/products/product_edit_screen.dart", "hash": "5709c2f322568ee87fda6a3b3276578b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/adapter.dart", "hash": "80079ed73f37411d422a28fb563580bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_parser.dart", "hash": "31c73410cd9adb292ff72d1bdf90f0f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_transaction_info.dart", "hash": "bb2751a286a6138ea0b34e5ce200e1ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/contexts.dart", "hash": "d41bc5eeeb919735a4abf5b776eaf7ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/range_slider_theme.dart", "hash": "6a9be91cde2a1628b1ee0613487b5438"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/type_check_hint.dart", "hash": "99d5cf39f88a466f4057d6aed9021209"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/shared_preferences_windows.dart", "hash": "2bc47cc0ce47761990162c3f08072016"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/histogram_series.dart", "hash": "9aae0ffe1a65132b9f6a4842ed67a9c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_impl.dart", "hash": "8e1d2c37f506b65c7d8b3274456d8dfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/operations.dart", "hash": "5dbef5156368d0f25b59750608e025a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_tonal_spot.dart", "hash": "da57aefc2c06656b68f2bb2639e78d33"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_stats_provider.g.dart", "hash": "5d90c004450454344af8172c08a0008d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "8c1a2c1feaeb22027ba291f1d38c4890"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/providers/admin_financial_providers.g.dart", "hash": "d0f6e4fc09587e5f4dec3361cf5f46d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/compat.dart", "hash": "75e9e8da5881b6c2ebedc871d7bbc064"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/sync_transformer.dart", "hash": "787074c3d370e068052721d16acefd9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/hilo_open_close_series.dart", "hash": "c0f501d283dc07092f80e74ddd538245"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/util/transform_empty_to_null.dart", "hash": "579bb0bd41c172690d80937bc1ce3b4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/widgets.dart", "hash": "592a041680dfbbd5415d8c67b70e9d97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/bollinger_bands_indicator.dart", "hash": "0f9053fbca3553327a23fbaad289080a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/custom_lint_core-0.7.5/LICENSE", "hash": "ed9a041fe7435b9b67080b2e9b56f1a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclientduckingcontrol.dart", "hash": "21ee375f5cb7acd3bec0129fba2839ca"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/models/admin_financial_models.freezed.dart", "hash": "71078c73f86b31f2d9c3765241e6e664"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/linux/file_picker_linux.dart", "hash": "f123e9c92c9377ad4cda446bad0ea784"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE", "hash": "c458aafc65e8993663c76f96f54c51bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mailer-6.5.0/LICENSE", "hash": "ab86ee75e8372c0b0246fddf9a26f66e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/exception_stacktrace_extractor.dart", "hash": "8af30d6b2f28a8a5f38660b7590b95b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "ccb3c80f13485133893f760c837c8b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/hooks.dart", "hash": "d418577dc36b8e9785ff4daf52f33546"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jnumber.dart", "hash": "3ac3393aac4a1621705984bdecfc94f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/accuracy_level.dart", "hash": "2455ca9a4568aebc8b2b4c1e7db044e1"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/simple_product_provider.dart", "hash": "c8ac42f4ed80bd29199b6d74a669d53b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/rate_limit_parser.dart", "hash": "fd2ec59295a333a5ae3bff9c5c9818c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_envelope_header.dart", "hash": "36b2951dadae0e73ee2d138974557bca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/category_axis.dart", "hash": "97db581b1074b761fc78490ed38121e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/enums.dart", "hash": "fcf700e37a2ca8372a19ea695ac704c8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "8ae04de7c196b60c50174800d036642f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/sentry_display_widget.dart", "hash": "cb2810bdc7dd350dd9bf7233e47618e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement5.dart", "hash": "7787380533fd85067e9c4303a9564dbb"}, {"path": "/Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "10f2d960c7d6250bbc47fdf5c6875480"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "659b88645890c6437ea5ce4928e8871e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/subscription_request_provider.g.dart", "hash": "99a2f66a93d4ac1e0c07a5838914e2d2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getsid_windows.dart", "hash": "659cff14f1665a31dec63407d7839624"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jshort.dart", "hash": "a117a749c9f95936f2f3f5b4f8c9acaa"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auth/screens/unified_auth_screen.dart", "hash": "9fe78ca8a5351f7440a6fe588bf05cd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/funnel_series.dart", "hash": "7dc25b9d7da701d2e7619e10c1f033cb"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/widgets/error_view.dart", "hash": "be721af24a95d2c6a8e8652de2a56a83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_line_series.dart", "hash": "55a0cc826debac10d0e842113b85e632"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_gpu.dart", "hash": "dd21663d9488f38b37374ca51b077ed1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/stacktrace_utils.dart", "hash": "6f0d07b7dfd8c0a548ec75a034cd52aa"}, {"path": "/Users/<USER>/mypro/carnow/lib/main.dart", "hash": "5b66c331b5ab8b8412a90a60a6e5deef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider.dart", "hash": "f186193f82036b24fc8379b1f332f817"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "f663757bacdc28f2692b30a293d75146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/tr_messages.dart", "hash": "a97587b4a082706d52af8193a350855a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/material_icons.dart", "hash": "f738a452e9d82127dbc4d4c91d9c03df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/event.dart", "hash": "1a7fe7a35dbd168a7f2e10065f4a3158"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/string.dart", "hash": "be2e3e8ab6ed0e2b2b554a26b78f91f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationsynchronizedinputpattern.dart", "hash": "dfa5338b5b93f9705e9f756dc0327549"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/providers/vehicle_list_providers.dart", "hash": "e088bf77e3d8f3956e56b38ba25699c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/future_group.dart", "hash": "fb71dd46672c822515f03f8f0dddbcb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_foundation.dart", "hash": "db8ef5ac4d806e72f7b356056cb50b1f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/services/product_api_service.dart", "hash": "fafa8e8d0e7f25fbc949ee9637b622f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_analyzer_utils-0.5.10/LICENSE", "hash": "5b4c5872973fd946c49c0ed7df0cb07e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_logger_formatter.dart", "hash": "059967845d24a99cb9f3572e09e7b8d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/image_cache_manager.dart", "hash": "69c7e246c8fb227cdabc8a3d9a8316dc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "15957b9d3eac4a2e1acaa24a3032afe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/constant.dart", "hash": "8d5660686b2687f3947b822758c82942"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationscrollpattern.dart", "hash": "d5e0952742a6404c71b939292023e2cb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "0c520a6b1ab38e0f294c3ddbc2ec9737"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/unparsed_frame.dart", "hash": "0c30a117b0d1fd5c94980510832b81d0"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/model_year.dart", "hash": "9a393f511d6f20a3d96d7cd1c1b0d97d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/slider_controller.dart", "hash": "9984b073e7de02b11486056254312df6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/scope.dart", "hash": "de9fb69ecd1d95e8b3596fec527a3ad5"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/config/app_config.dart", "hash": "1316e6680cc1795045e21f332c316528"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_notifications_async_provider.dart", "hash": "357a8c2a6264b9393587a8e7228119b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_name.dart", "hash": "749e18efee29d6925d7c55e573d3eb2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/platform_file.dart", "hash": "d72dfeeaec3f8fc748ad591a99a5dd52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_package.dart", "hash": "0ab7c809dc10bd454deb245f895f5729"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/user_interaction/user_interaction_info.dart", "hash": "c7582324c2cb01c5398d1a40a7b6ee54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellservice.dart", "hash": "b92ed7d96a5284441953017edb47f285"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/rate_limiter.dart", "hash": "39ff31de2570cecde6d68c2e8bdd6b4f"}, {"path": "/Users/<USER>/mypro/carnow/fonts/Cairo/Cairo-regular.ttf", "hash": "45aaa2b5f9de1d61c2d3fe1f40107ac8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_fidelity.dart", "hash": "2b93d5d6981f0c021cb892223416927d"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/assets/data/brake_systems.json", "hash": "b47a2b337cad5e29998c7b51ec00aeff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/local_auth_platform_interface.dart", "hash": "cbdfe985d2a7b78662d58e05cd1dddcf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "822ae20c3b70355a4198594745c656f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/util/jlist.dart", "hash": "e4fcf798f6fe8e5b4abec3abc8ac5ce2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_content.dart", "hash": "29d2a4744cad0979d4cfdcdc2c1a6ed4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/repositories/admin_user_repository.dart", "hash": "387b9780052417b4eb6bb36a37a54389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/bound_multipart_stream.dart", "hash": "6a792eed43130ef8c5b35bb12106f303"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/platform/_io_platform.dart", "hash": "505aa057d0576a08e978780761b80ef2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ro_messages.dart", "hash": "08d5518130c41be82a3bedc95abaf928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isimpleaudiovolume.dart", "hash": "a064bc8b49ee4e47fd7b996364a8469e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/scale_effect.dart", "hash": "44f6353e30680b4be38209f467c94c80"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/tools_details_screen.dart", "hash": "6ea2f590cd4de20d0086b59f35320ebc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "377731ed35ad8d1d36dcfd532a3d308e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/on_error_resume.dart", "hash": "f79083ce7919dc45b4d2c313bd37af7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/find.dart", "hash": "9f4ec4b8e28d5bf94cbd385aa48eb91f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/helper.dart", "hash": "f8bd9032103c30d639f265b8099fb772"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry.dart", "hash": "2b2dd1515da9cfb2765a8a00dcf3a934"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/hi_messages.dart", "hash": "8d4e0d6959f589228c8861db63be887f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/LICENSE", "hash": "9741c346eef56131163e13b9db1241b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/iphlpapi.g.dart", "hash": "90687597d058691ddabaa9819ebe2441"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/repositories/category_repository.dart", "hash": "93d13af4508eb538bded35a6479f80b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/sentry_native_invoker.dart", "hash": "e075a73855f965836360107c1193f467"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumidlist.dart", "hash": "7d1806cb19bc0d23a18c2760d106d95e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/sentry.dart", "hash": "a3bc20b01392d6c9f56f9d33832ace0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/materialize.dart", "hash": "7787d9ce2aed834062cd38b022824d31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jcharacter.dart", "hash": "9d33a404564ccb62d70af75dcb109deb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "1ae1a412c9f9daff34b9dd63e60cec2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestapplicationsenumerator.dart", "hash": "a0c11bb2957ee28a1de2145cc233367d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "25b96e83b1368abc11d4aeae19e9f398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_settings_real.dart", "hash": "81f9396d070e076eb99ccb41c0be5879"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "33ce088a133276cbfd4a33ec49bdcb62"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/providers/admin_wallet_providers.dart", "hash": "030d8d4287f2cdefe46fa60e3a1ab452"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/atr_indicator.dart", "hash": "00978f9451272b72916879ed66a61bcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/EvilIcons.ttf", "hash": "140c53a7643ea949007aa9a282153849"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.6/LICENSE", "hash": "********************************"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/subscription_service.dart", "hash": "a36c092e4f954f13385c82874d560e18"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "830b9f37313c1b493247c6e7f5f79481"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_specification.g.dart", "hash": "aa68f53a9fe4545a886deff4d2fb9e54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/base.dart", "hash": "86039b13313ad468f867bb5522411241"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/palettes/core_palette.dart", "hash": "5a11d4662fc61ed2bd835a4e6bb0f47f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio/dio_for_native.dart", "hash": "6f053637ded96c67b342e6a80e7372f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/behavior_subject.dart", "hash": "86d361932e590380696b3189090d1034"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-6.0.0/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auction/widgets/auction_timer_widget.dart", "hash": "5b21a6f22a06bf6d41123908741944b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/pt_br_messages.dart", "hash": "e08f3255461a95f8c0f48435658a8163"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/types.dart", "hash": "f4d93b039bc86c4a156848d06fbc2917"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishelllinkdual.dart", "hash": "75335c9306751e1de52734c1ae433ac0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/file_output.dart", "hash": "7dbee69bb2d6088496e7d7bbdde1ccc8"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/providers/auth_providers.g.dart", "hash": "f942ee10992e61f211936da6459ed81f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/mime.dart", "hash": "6438480f29034a2c6acd5817c656d94d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/providers/add_car_provider.dart", "hash": "724a085f49d2679ab72e27487163b668"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/progress_indicators.dart", "hash": "c2c2286fb7180be54cc4fd8b03ba9dea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/jvm/jvm_frame.dart", "hash": "9efc18aed2c8a8348ccf1d407eb68252"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/widgets/loading_indicators.dart", "hash": "b87a97fd2e0a0ead112a7a55de44622e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/MaterialCommunityIcons.ttf", "hash": "b62641afc9ab487008e996a5c5865e56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/multi_image_stream_completer.dart", "hash": "4870aa3bcaa04ecc633da01dbd2c9560"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/sentry_safe_method_channel.dart", "hash": "959344c88b2394c28129a9e22201153b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/router.dart", "hash": "d4b70b211f5016be44df4f0b02b8bbad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/src/messages.g.dart", "hash": "bee9a89328e73d06f9b915e157deffe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/character_data_parser.dart", "hash": "a3044567a5c6d8b0e52367af1a23d5e1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "eda0152837e3eb094d8b1f6d0754f088"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/doctype.dart", "hash": "c2d76b78fb107e358b1ad967f15f1746"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "019f7b771f1865632d5a36c9e74296db"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "50dfb9886f462e2b3405f0f8d23f179b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/models/product_sort_by.dart", "hash": "7a492c9aa92e1f59178c091fcd3af711"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/native_memory.dart", "hash": "ba7d341ca61d143b077fc18f8656d1a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/start_element.dart", "hash": "e11fc9210b4438654c11893b98ac66fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/plot_band.dart", "hash": "ec87fb9fac56d6c68bbf22505d41e6f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/dynamic_color.dart", "hash": "e0469000d5e6f512d2fccfd97145c727"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches/matches_iterator.dart", "hash": "4c92351d347c52a00797317aa487600f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE", "hash": "b2bed301ea1d2c4b9c1eb2cc25a9b3cd"}, {"path": "/Users/<USER>/mypro/carnow/assets/data/transmission_types.json", "hash": "21434644932cb8ed0074ff563a220eaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_view_hierarchy.dart", "hash": "2e2ffb0a216e55a7a8d90be2def0c8d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/date_symbol_data_custom.dart", "hash": "e68673efecc46d6f63304c37b01a5b90"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/http_header_utils.dart", "hash": "024732fa5d91958856752ad7f1b59f07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/async.dart", "hash": "bf00683339299d8da0ea826f185e6ca6"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/extensions/user_seller_extension.dart", "hash": "afb81af41db2eef9302c280fa5a61062"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_generation.g.dart", "hash": "f09dd6b5fe43a63d864e2d36c6201dbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sqflite_debug.dart", "hash": "a2cdec29e909752629150b24b9b18407"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/third_party/global_env_extensions.dart", "hash": "ad4e1a8d950f4a02da1d8a065f5fb11a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_column100_series.dart", "hash": "40d779b2869fb13ea0632eb873743461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement2.dart", "hash": "4f061ba7ed2e408e218e0eb4375dddee"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/assets/data/automotive_reference_data.json", "hash": "615a30fdd84bff40d9991ab7b0ce6392"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-6.1.0/lib/google_sign_in_ios.dart", "hash": "4214c2dc5c8a61fb40617b7a42f1d61c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "1bdb47a9af4b0a5d759937da8ff04db0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "4f9995e04ebf5827d1352afca6adda26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "hash": "092362603d55c20cda672457571f6483"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "bb7bcb463df2ae0f5f952d439fdb384e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/connectivity_plus.dart", "hash": "9b43d6f9384a837bbd0d8474e2365c7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/subjects.dart", "hash": "1f923e6c3ab08753130b61ba27256959"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/open_options.dart", "hash": "296e60aee7732b001a79f3216058a381"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/octo_image.dart", "hash": "66d6d10e44ad1e696a8e632a5c4883d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/waterfall_series.dart", "hash": "7743977263146fcf493f52b357579db5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.2/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auth/screens/forgot_password_screen.dart", "hash": "b70e3059cf1c0ffa7908943244fb81a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/lib/src/corepalette_to_colorscheme.dart", "hash": "258070c8c54a31c35762cd8aa5b8a605"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a2d1c7bec7b52901761f3d52a1ac02d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/stream_output.dart", "hash": "b0ad7758ab1a2dc1b0b8bd30c1978d47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/noop_client.dart", "hash": "7fa2c1940b25ca41833aa0ca7a7e8f1d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/sma_indicator.dart", "hash": "e7c50fca7553d0087c626105b5aa5f8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/compat.dart", "hash": "8a31d0709de6865d3f49374ab6bb274a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/span_status.dart", "hash": "87ea487272f3d07ea09f990843c3f207"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechwaveformatex.dart", "hash": "8d9c84de01d7084606586631ac759a34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/utils/color_utils.dart", "hash": "a97282161191c89d98231dfd655252c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gap-3.0.1/LICENSE", "hash": "5dad3c4cf0dbf516f70de29802f55fd0"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/city_model.dart", "hash": "29263049f75b41f18a1ea880e7c74b5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/utils.dart", "hash": "599be812b0d48a34af027e2c896771e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/default_if_empty.dart", "hash": "527ad391c229e34074a6d5c1aa656133"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_logger.dart", "hash": "099e1b7e7f8e4413c1c18d402e89df57"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "ee62fb3be5d885d65054fac4b84cac6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/factory_impl.dart", "hash": "65614758273c0b82b4ce22b3728be36c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/LICENSE", "hash": "274291edc62b938ad94e61cec4a14bec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sqflite_logger.dart", "hash": "6745a4321f65340dc91faae80415984b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "ff2b2e7159e19374f968cf529da25c01"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/exception_cause_extractor.dart", "hash": "e29a482c5a9807cc9cad60b0902cab2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/inline.dart", "hash": "7cfb88f7da0c2022734fb4c438317d95"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/hct/hct.dart", "hash": "d75fe23c75429021b8f7fe4e57ac5d49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test-1.25.15/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/providers/providers.dart", "hash": "b0d00d1dc8f23b41abececb2711edd47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/min_max.dart", "hash": "50c5f00339854085c2f637109c4166f3"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_models.g.dart", "hash": "12598709d790fff4ad7d9e70ee02fedb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "cd995d0f309bf74d0bbe94eb1e4e8e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/builders.dart", "hash": "dc1a141705a29df814f129c65b47b5d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/x_type_group.dart", "hash": "826066d6663c91c94cee09406ded70be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/image_picker_macos.dart", "hash": "0f0fc7bc5c7c2c1581ed2ed245baf136"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_comm_l1_1_1.g.dart", "hash": "ebf62f8040320f913d52494eab3f3dca"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "hash": "a4578c9e939f9a7aec6e8897e055b6ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/console_output.dart", "hash": "3430401759c3faf2891f666c719a4c18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/json_cache_info_repository.dart", "hash": "f8113503c91cd843d744fa61b0b15ba6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/allowed_keys_helpers.dart", "hash": "41696c18e708950dccaf7cb4f5b7b195"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_member_name.dart", "hash": "2ef397117616f6ff779ed0ab2dd0d61d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/url_launcher_platform_interface.dart", "hash": "9190f2442b5cf3eee32ab93156e97fb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_feature_flag.dart", "hash": "cb0ffc0c4160af2d94594191605aee0e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/utils/date_localizations.dart", "hash": "eab3afdf13cebd3927cc12a7a8c092e2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/doughnut_series.dart", "hash": "1cc313e238191db7110d1670dbbc6e1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "6fc640633e357a75291efec1c68b02ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/widget_filter.dart", "hash": "7a23f75b88419507b0171fa1ad55d3b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/third_party/generated_bindings.dart", "hash": "8cd7e17510850fcc7ca08e7a3f0c26fe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "068ea69f3733bd1aa72b910e51b41b12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.dart", "hash": "c6f78ebc1239a030ffc141df9b33aed1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "5d99a505ddc69d5accc4e5a83f5cfa4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_buffer.dart", "hash": "99760254cc7c1941d4d7d7bb0fad045d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/platform/platform.dart", "hash": "17488cbfc8b9ee2e6e5ba0229d7c21a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/flutter_error_integration.dart", "hash": "c4a1bd5663d56647e357a0c44549233e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "4201a655a36b0362d1b9f946b10b5e5e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_model.freezed.dart", "hash": "6b700ac5592a035fd1b11d2b292bf15e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_item_type.dart", "hash": "a5198b7b2672eccc5d5cd26dad26b3ca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/flutter_version.dart", "hash": "ad5b018b42f4cfaf02739e10a48c3ca3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader3.dart", "hash": "e97932f0cef53e2c018203ac3cf1c7e4"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/carnow_providers.dart", "hash": "1b150a0eee608f57a2ef1d530438193b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "e472fd233266592e97b3fb39bb1a11dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/node_type.dart", "hash": "57e5dc91c30bff1774eaaa45a798d0df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/nl_messages.dart", "hash": "1d0cf1d8091b8ceeb53a29a6053b5d2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/multi_output.dart", "hash": "8a8ec5edf7a4c3d3a3598480901db44c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_completer.dart", "hash": "2430a12d4750c3c76ef07d29bb6f6691"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/carnow_transaction.g.dart", "hash": "19d10f46a5341c64bc75d2898be794ec"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/utils/validators.dart", "hash": "e6c4ea7e9ff28a4a321f6c674ae0e4b2"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/maintenance_details_screen.dart", "hash": "023eede01ea46ea4ba8b6a998b9e1ee5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/LICENSE", "hash": "d229da563da18fe5d58cd95a6467d584"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auction/screens/auction_detail_screen.dart", "hash": "f393789f8acaa2e9576516c2e65908b0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-3.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_1.g.dart", "hash": "8944748ddfae167a4c9f3dc75a702e46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider.dart", "hash": "a6705b39e0c01e2fc0e40b8c8c674aac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/subscription_stream.dart", "hash": "45f0e675fa74d765bee71cf2c553bd58"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/providers/products_provider.dart", "hash": "87621db5e7729e8821a28c79ab656bff"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_conversation.g.dart", "hash": "ef2d48de1b56b625f3c2863b7a65e3f6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/line_scanner.dart", "hash": "168bedc5b96bb6fea46c5b5aa43addd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/date_format_internal.dart", "hash": "125a884a4733a2ef5a572ae55d49e678"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "12120b49ba363d4c964cf1d043a0aa1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "e758d8d6b65597325bd35b5dc769c7a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/history/providers/history_provider.g.dart", "hash": "619265a9d7a278223e50d3fdecc10bca"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/section_model.dart", "hash": "53cd162ea2443e34cda2c7f6beac2ef6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestpackageid.dart", "hash": "88956349d04ce0c5fc6ae1e89fd65b2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/src/messages.g.dart", "hash": "d631809a6f4e20b7aa9ea7e17a6581de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_feedback.dart", "hash": "a3455a1f72166283fe46c1de8e5fd9e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/reflection/iterable.dart", "hash": "763f95cfee7dc8f35f6557eab7e94312"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrange3.dart", "hash": "4f4a2d291e23c96c7ae0d4dbc9598c54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gvariant_text_codec.dart", "hash": "faa053ac2743940afb0f37b027f85c12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/widget_utils.dart", "hash": "4a8a4f8953c9c9c67d396efa599d8f27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/core_tooltip.dart", "hash": "d868d903d4216cefb8d599a6719f9348"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/lib/src/dynamic_color_builder.dart", "hash": "c6f2a9b5b4628cb1a997d4939ca579c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensorcollection.dart", "hash": "c20dc5b81ea6dddfc61f66c603afd971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-14.0.2/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/LICENSE", "hash": "73f9b9d309f8eea6cde4289124f19268"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/lib/src/messages.g.dart", "hash": "9d93cca48719b33aa20e1a2f3051936e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-2.0.0/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/comment.dart", "hash": "87546066dfc566126ed9357805535e97"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/FontAwesome5_Solid.ttf", "hash": "c867d50f0b1444a9d91e516e0f51fe14"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "58707cf455f97f907192b4ff92d36711"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_error_name.dart", "hash": "7398500b1824f6043f23e208cd993866"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationinvokepattern.dart", "hash": "942a7879522bdf82258a3383893665a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "4f3e0e3af33c5bdfbf1d32adeba91652"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/consumer.dart", "hash": "f28a95b717859fa14ea8344e766e7fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jni.dart", "hash": "e8b344a788de0440d592ea2871f06cbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationobjectmodelpattern.dart", "hash": "93fd05191baf9bfae1ae604f67d953b5"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/enums.dart", "hash": "da582f59d7662ae93eb43128553fddc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_link.dart", "hash": "92be3b74ebf2b10ee5852ddbbc825971"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/enhanced_secure_token_storage.dart", "hash": "3358e1b77328dd818a98010defd0b84d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/clock.dart", "hash": "84ad21db5ba97deb809b65697546e39c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/providers/admin_user_providers.g.dart", "hash": "467c70b92b55a2e5da47e286a4c12e9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/sentry_http_client.dart", "hash": "69a83b5698fc309cd0bb79de665abd88"}, {"path": "/Users/<USER>/mypro/carnow/assets/images/logo.png", "hash": "f45e96502f36257b225bc8c2672097ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/blend/blend.dart", "hash": "7594a28b48f672ffb4829ada9ea4018e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/noop_transport.dart", "hash": "5fe7fc237cc2908a8d8d193eacacc3db"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "hash": "8af2b6b4632d42b4e1701ecda1a3448a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ko_messages.dart", "hash": "bbcfcebc98d822265508d95c7f9b4f27"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "2adcbf9fb509dd8fe8864a702db29043"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/ntdll.g.dart", "hash": "72e3f09580a88c2aa3ce838611e0a25d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/auto_dispose.dart", "hash": "d2e52f81da2329303a3f9d4b369c3320"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_dialog_options.dart", "hash": "c7a750b73798e6fbab221eff051e22c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/vi_messages.dart", "hash": "a7eaabaee9e45b9de35e06b27e3728b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_winrt_error_l1_1_0.g.dart", "hash": "ef5d77a8181065ceb0e93986c1a6f6ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/gr_messages.dart", "hash": "e2aa1620aa833035d1cea0e0556b7bad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/image_picker_windows.dart", "hash": "4a9b1f00f6665e425a008a2201361658"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "873f01c9dae2d98c8df6fc08ca543aca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/recursive_exception_cause_extractor.dart", "hash": "6b708ccb530cf8545e25f2029d391e90"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "4ec9c8dd6d6ecb43d26ebaef03abd1ab"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/datapager_theme.dart", "hash": "69ee368078c83a95caad9d9e1b263f02"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/recommendations/widgets/recommendation_group_widget.dart", "hash": "27075f3fc4cf52ddd3f38150fadb1dbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_column_series.dart", "hash": "736d1f484317eedee699ae6592c23c51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/filetime.dart", "hash": "562889498a1b0cda759a1186693143e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/eof.dart", "hash": "6a083480a6cb878f98927a9271454bd0"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/carnow_wallet.freezed.dart", "hash": "01ce8126883e8cc38011d20a392d9ed1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/screens/screens.dart", "hash": "5874032451ef0a7de4d5bb4c918d733a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/normalizer.dart", "hash": "5684bfb4916cd4ec19596b3fd6a7a93b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationdragpattern.dart", "hash": "51d92d191bdfceacf4cc7381782d4e5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/sibling.dart", "hash": "6cee72f673d593b0b84628bf243727a8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/netapi32.g.dart", "hash": "242d63b96e4a26d3b557a32d0a008a4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_2.dart", "hash": "dc92a928880163bbe0232a641f7f4276"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/widgets/error_message.dart", "hash": "75038699186e8ed300ce18a1b9704f40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/quantizer_wsmeans.dart", "hash": "907dee395c762afbdcd8a23c105e9cff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/be_messages.dart", "hash": "1b2008d0a65366a516f2d896c56e5d7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/filters/production_filter.dart", "hash": "d455a0ea71515758776153cc65cb1978"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "42c4c0281ec179aea5687dbced56aca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/url_launcher_macos.dart", "hash": "ff296a17d3582fcd8fe99bfb544a3978"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/matcher.dart", "hash": "faa18ee55924a5c65995875c94338d98"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "bf00ea3c58b6ee2b3f5422cfc3e3cd2b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_application_notifier.dart", "hash": "65e06c048a62b579abfb7ec2a42d3b86"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "c97a8ffd51479d05a18a54ac27ccba15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/LICENSE", "hash": "d229da563da18fe5d58cd95a6467d584"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "e0b4c38191be9320c3113762d2dfebbb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "0bc495ddf9b02a06a5fc6934847e8708"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "a4c1dffb16d559eb4d22bac89777780e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "6a7d9ee6c8fae5e9548911da897c6925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/dart_exception_type_identifier_io.dart", "hash": "07849435c4726babb513b4ed9884ad0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtransformpattern2.dart", "hash": "10ee0ac3bc045cf4344c623f4396d941"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemarray.dart", "hash": "bd08457ce7d378f126bea891f669b69b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "5da306e7f2542e5fb61efff6b4824912"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auth/screens/email_verification_screen.dart", "hash": "d84e6dea012e9cc30293aa23865343ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemlocator.dart", "hash": "84516bb884e27c54321d286d9ae9e9d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_expressive.dart", "hash": "13b2049dce60861c73d20ad96c34da30"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/assets/images/placeholder.png", "hash": "83b57be3c88253ae08a43558c1715376"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_parser_base.dart", "hash": "39348131fc86fb08a42dd6b2d1b16bf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/lib/src/share_plus_windows.dart", "hash": "e01a892bce9721df8cd777da8b8609f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/circular_data_label.dart", "hash": "9745410bfcdf8be49afb9f6048b126e6"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/widgets/loading_button.dart", "hash": "466335eda4ad2410b412b181763e491e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/constants.dart", "hash": "195aceb9dfe0dacbf39711b8622ce2b4"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/notification_api_service.dart", "hash": "d259ef0526ad0f684c9052e61f9bdf4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/candle_series.dart", "hash": "9c2d479369eb852ee26caa883773e055"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/unified_auth_provider.dart", "hash": "10f41ac73072467fa24b8a44f9adaf6d"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/subscription_error.dart", "hash": "99089322d244d0bfec00b4a901e36860"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/package_info_data.dart", "hash": "e1e3a7882488820f09a44a49d8efed8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/accumulation_distribution_indicator.dart", "hash": "3d566425eb5d9781a386a7bedd7e62b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/font_awesome.dart", "hash": "27c9991574e7698f79e6e6b32bc0cb66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensordatareport.dart", "hash": "d241941a5420f01b81ee47fc755f8123"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_trim.dart", "hash": "b351a39cef4dedc1a5350bbeb3deb231"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/race.dart", "hash": "2164e0e3bc00d90bd03708ddfd475ad9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/cached_network_image_platform_interface.dart", "hash": "d9ccb5a0c8dcf64361a257c101d0e719"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/flutter_localizations.dart", "hash": "dc4a72832b8b4320c2130207ff161b58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/frustum.dart", "hash": "218ecb2798a6fb1ec08cd5c993d98269"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/assets/data/trailer_types.json", "hash": "807ff6d1208d1267ba822ba5bff0f400"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/ansi_color.dart", "hash": "2008a57b1ec04a349e6e8c7563f41418"}, {"path": "/Users/<USER>/mypro/carnow/lib/l10n/app_localizations.dart", "hash": "ec0118b06afc4fddf65ec55d27158f41"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/logger.dart", "hash": "0cdc9d79f7fc4d0920bc6a8fc02e6872"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_interface_name.dart", "hash": "4f835012742ef22df8c85292594f9823"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/flutter_secure_storage.dart", "hash": "5a944801c9b2bd3447f982168b31e46c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/expansible.dart", "hash": "43bc92e2816a78f5d5987930bc3e804d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/reject_errors.dart", "hash": "2f711a88a049130159adb3f7867423c0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/breadcrumb_log_level.dart", "hash": "c653a28ad8610453bc2b1ea8ee12e580"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_winrt_string_l1_1_0.g.dart", "hash": "05fbdfb1bb8ed12098aa521c31a145ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_options.dart", "hash": "efa5cfe11a40248da11fc4c0527f7a68"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "0bda807c0c8098d0ca933cde19f49516"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/auto_dispose.dart", "hash": "9ab6d0a38467598c8e1f332648cff545"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/predicate.dart", "hash": "4fcb0c3d6a9c166d16c124c91e33dcb6"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/Ionicons.ttf", "hash": "b3263095df30cb7db78c613e73f9499a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/models/product_model.g.dart", "hash": "12dd672aa989e3a218a9a44152108c37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/future.dart", "hash": "18c04a8f8132af2c1b1de5af6909025c"}, {"path": "/Users/<USER>/mypro/carnow/lib/navigation/models/navigation_item.dart", "hash": "702108de0ecd46001142a13bfccd40ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/java/binding.dart", "hash": "b567eb4ac986fe587850a02aef61d1da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/lib/src/types/foreground_settings.dart", "hash": "dce1bb0889d179dfe07dae4a519b6ccb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/errors.dart", "hash": "246aa94bb2d86ce16c59b69b13d42904"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/blocked_users_provider.dart", "hash": "6702eff75a62943f7b23412c7026c382"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/.env", "hash": "ea56ad479491ea036244f04df39c416b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/integration.dart", "hash": "641d28d768da136359fff4b2663d9edd"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/providers/spec_filter_provider.dart", "hash": "e8c029f02618fd2c5e67a12644fa5a78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2/LICENSE", "hash": "4076b6f7ff021f511a11282f13f0c61e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "9802442b82d3be84abecae8d0a2c7bd6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "805f831d339e4ab9e6b172b2bf845809"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/file_selector_windows.dart", "hash": "0902c41eed709a7841f11130fac2a593"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/comment.dart", "hash": "74fb000405fb96842a3ce15a519d8ae8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/lost_data_response.dart", "hash": "064f79178a908761de1a6b8334a36b6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/framework.dart", "hash": "c35452c8c9e23ae8fd28123151ca0a25"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/screenshot_support.dart", "hash": "962031bfd337bf7d16c2d21cd1fa4443"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "8e870f9527626d34dc675b9e28edce85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/replay_event_processor.dart", "hash": "3424eb8e0a0bae037c5a04b43193aa92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_user.dart", "hash": "d8eb36e716159568d0eeac57c59d0250"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/LICENSE", "hash": "43465f3d93317f24a42a4f1dd5dc012e"}, {"path": "/Users/<USER>/mypro/carnow/fonts/Cairo/Cairo-600.ttf", "hash": "d24823f3136e983e632a2d8e11c822b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/biometric_type.dart", "hash": "5c67019c52b8cc1c9e1d211aaca0f2a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/parser_match.dart", "hash": "d742d41268dec3da5e669142ae344928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/file_service.dart", "hash": "857464ce6f576c4020591d501ebcaaa7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/native_app_start_handler.dart", "hash": "9e6f69baf8602eb238110dc5a8357d6f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/orders/models/unified_order_model.g.dart", "hash": "39ac63c8b480f029eee534ca0400c3ae"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/section_model.freezed.dart", "hash": "ff2940e74f06b4c458b7b978ca82aea2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/any.dart", "hash": "35536afe2f48001aa7c588b131051b83"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "c816d604c95b060fbb4fa0831ad7523d"}, {"path": "/Users/<USER>/mypro/carnow/fonts/Cairo/Cairo-700.ttf", "hash": "3309a2d0fb51cb2e3335b8a56f3bbf63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/smooth_page_indicator.dart", "hash": "9154f4277b5e55ef82a0ce45258038ea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "cd0cbb4d29516ed6b03d1c68f0c08477"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/focus_node.dart", "hash": "c7a92ad7cca82719c2f74dde16511375"}, {"path": "/Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/providers/admin_management_provider.dart", "hash": "62fa50482b3a9bc165e7a9be53e7206f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/raw_menu_anchor.dart", "hash": "a749880c7b2c93609c79f05151beda3b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "eaed941ddb98b44c090d06e0be0a7562"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "d110c5e3ee26058a3e9b4bba6440f15f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.11.0/LICENSE", "hash": "b2bed301ea1d2c4b9c1eb2cc25a9b3cd"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/recommendations/models/recommendation_model.dart", "hash": "862255c178734e2f6fdb65ecf2a94563"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/enum_helpers.dart", "hash": "32c8f2d4dc53cfe56f5fa637be2c52e7"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/routes/subcategory_routes.dart", "hash": "5c9e16f5fa85c0c1f20a771f77920a98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/compat/file_fetcher.dart", "hash": "808718676ca459f8941aa519a73bbff2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement8.dart", "hash": "befbfd864024e35cb6b7752f9b4ac4d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iknownfoldermanager.dart", "hash": "49703a6e2b7dff13d801b6eb6e02062c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_envelope_item.dart", "hash": "d198f1bccaa10f2a44834bdec20dd5c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_5.dart", "hash": "a2f8f444e0f2d008fef980dd8df0bcac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/stochastic_indicator.dart", "hash": "51ae5905b1d36c3b4f5cfb47f26a105e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/store/screens/store_screen.dart", "hash": "93327ccd3ef25798cd28a9806ecbbf07"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search.dart", "hash": "66a927b3f610db5ff8c77a6ba3ccee0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_directory.dart", "hash": "96ef4798e4cf4560148762dd71bd180a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cli_config-0.2.0/LICENSE", "hash": "5333d7329ee0e268134b45a6622a6b80"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_dashboard_provider.g.dart", "hash": "083d6104b49bbc6c974ef32121fbf4cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_linux-0.2.3/lib/src/geolocator_gnome.dart", "hash": "8beb02de0c81e1e36d1d533331d41fb5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/error.dart", "hash": "a10eafbc71350955a16e4e787402780b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "7706f479f74f6076ef8113576fe54749"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_models.dart", "hash": "26fe936b3276d73c9675a1a5005ebe4e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/chat_providers.dart", "hash": "66d2f338a944526df11e9b2193fc38c1"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/material_color_utilities.dart", "hash": "fbd82bcbc9e665b8d098f0716297aa14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/accessors.dart", "hash": "3a4b332b8fcf7dafab60019aa6a0217e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/nb_no_messages.dart", "hash": "c25db1d18f943e8c111a995433ddfbd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/cocoa/sentry_native_cocoa.dart", "hash": "8c8751e458a0b727b0dcc4342f896cd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/parameter.dart", "hash": "08b1358e505b0414dc60489b750ba2b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/load_dart_debug_images_integration.dart", "hash": "b71b4952d6ad214cb33171895281251b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/route_data.dart", "hash": "6fb769cf3f98ed969c465b682cbc24f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/family.dart", "hash": "751c8376ab9bb4a866f5db6d7e6b864b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ierrorinfo.dart", "hash": "7ec176456b796d360121e28a6af41a2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "0d1b13fd16692571d5725f164d0964ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "a2587417bcfd04b614cac5d749f65180"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/context.dart", "hash": "a07f8e10a45176e8210b1bbac38f3e1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "d6008bafffb5b2e7bf16e59a9d3ad934"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_model.dart", "hash": "65eef4b15accbcb60d255f208a20882a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/shared_preferences_foundation.dart", "hash": "b72ebe27944e3a75601e56579bb92907"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "40d43557904504dbd816a205b73461b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/win32.dart", "hash": "018e93669d12c52b66204d139de58ef8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "d9eb28b2265932eb628ad0c3a123bee7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "eea9d5a977d3ff4f46bb63a0f140c738"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/connectable_stream.dart", "hash": "fcbda87916b8b2c4f7b88460ac9bb415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/set_string.dart", "hash": "097e09840cc00325fdbebaacd05f4827"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/breadcrumb_client.dart", "hash": "a916fa01cc94eb96163ca536579e1e7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/extension.dart", "hash": "ef82a025843a9945bb252078a9754fa4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_isolate_extension.dart", "hash": "e0363ecfdc3c15b735ceb9337151d871"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/dialogs.dart", "hash": "31ff0d4d17e824e16798aed227f48e88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/greedy.dart", "hash": "c138ee7ea69a6621403b3ba6973d4d7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/momentum_indicator.dart", "hash": "ef186a0ac7ad080acb391c6887dd12dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/map.dart", "hash": "822f0a79dfd6a3c997d2b898ec420b97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/LICENSE", "hash": "44fe697f53f8b935095a3071bea80b48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_linux-0.2.3/lib/geolocator_linux.dart", "hash": "8dd181e444b51c85d8c79e6d61908abf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immendpoint.dart", "hash": "ceac2a8f7197831de70d242e885a1527"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/linux_options.dart", "hash": "26c4f0c369b83e53900ac87bf7e0dcff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_store.dart", "hash": "b72b9cd4de477e80296c7f58bc9f5f30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iagileobject.dart", "hash": "4bc403cec1c5846051bca88edb712a8c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/default_extension_map.dart", "hash": "fe2df60ed5b05e922df2ee9fef5cf5d9"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/chat_backup_screen.dart", "hash": "71009a167989c9c54172f0b27838a1d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_legacy.dart", "hash": "4144d8b8e1cae585ab9f01406b3e1f75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/category_model.g.dart", "hash": "84b6713c8458423f156ade1b3d4b8d2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_event_like.dart", "hash": "22d9963849efbc3fc69c603086b9e452"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/pyramid_data_label.dart", "hash": "07dcfb8e5fb7012efe34dbfb4b5a72e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/rw_messages.dart", "hash": "e05ff10ffecaf4b5a62187144eb03ffe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "8b65a0312de1594ea0989e8ce1d4b257"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages.g.dart", "hash": "d8a6ceefc2ed13b75c503d01c8911fd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/profiling.dart", "hash": "2c829b6cacc339c8fd121b9bbf43c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement4.dart", "hash": "98e80e3c681156f330d79925f2675eb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/geoclue.dart", "hash": "395cf6b4c8ba1fae9e4a0e456ddf4196"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "2cf5ffb71954128b5e80f17a36bcde43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/ref.dart", "hash": "452cd5bd89dd73f555cc1ef42032e1f8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/sparkline/utils/helper.dart", "hash": "b996f6647aeeb4e5184840d152b7439e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/optimize.dart", "hash": "a4acaadf6a817daad4c485f9c6741bca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_read_buffer.dart", "hash": "fd517e61edeaf09f9e4cf9e9ba8af13c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/text.dart", "hash": "f52860ffbd4c6858f092292d1589d556"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_mixin.dart", "hash": "c84b28531f935f9c194ae3a92e38199e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_format_field.dart", "hash": "71a8fb28c6cc831bc9bc7c636575765b"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/favorites/routes/favorites_routes.dart", "hash": "731ceac1e50d2c719498c6a922f66ad2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/Users/<USER>/mypro/carnow/assets/data/brake_systems.json", "hash": "b47a2b337cad5e29998c7b51ec00aeff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_3.dart", "hash": "050f96bbbf01a1f86e208d7d8cc08901"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/printers/prefix_printer.dart", "hash": "129f33e0f404d9fe5ef3eb75dd7762e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix4.dart", "hash": "6250cc05770b9eca7a8010eaed7e5b94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/logger.dart", "hash": "49b829330c9d1fa06c2856f5f2266921"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetworklistmanager.dart", "hash": "9915c7d7ab3c9994e77dc4abfba9418d"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/theme_provider.g.dart", "hash": "f904aa6e9d77837abf8267651d60f267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/dotenv.dart", "hash": "fff38fbee67e2f21ac8b871431f3f5aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/continuation.dart", "hash": "33717fbf3f4de35b5e494d284a252bb7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_sampling_context.dart", "hash": "a28288711a7d76a82acbcc0b26272b73"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_notification_model.dart", "hash": "1cd45df3dd1ebe15903c542628129f0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/load_debug_images_integration.dart", "hash": "fe17da55ae054bad18a92659d928aeb9"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/theme/app_color_extensions.dart", "hash": "29cffb7b1b196bc2973edc68c7633579"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/comparison.dart", "hash": "643ca26571c2ba94477233dbb914b1ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationdroptargetpattern.dart", "hash": "45a4d78a037bdf56e5eb45d75298ff84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider.dart", "hash": "332fc1055d849f61ff8cb6ab6a919d1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/oleaut32.g.dart", "hash": "d36205839f51ee14bc2d832726c52853"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/auto_dispose.dart", "hash": "ef220252cc1911073575cfbf66f4c8d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/third_party/jni_bindings_generated.dart", "hash": "0e860e4fc36095c56c5ac67ea36a37a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose.dart", "hash": "19ad3f559f8a8ac66bbf9a697588b5f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/prefix_name.dart", "hash": "fbb3e43ae57262b3fc190cb173a7b5bf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/java/sentry_native_java.dart", "hash": "b2647d9aeeb107c63ae8a8b3aeab9d89"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/providers/garage_provider.g.dart", "hash": "26032f433d000ec6d0a49083937ffb15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/container.dart", "hash": "8597f18181783d905e40dc64f0c0555a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/package_info_plus_linux.dart", "hash": "153e569a429470f19962e80723cbf73f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/frames_tracking/span_frame_metrics_collector.dart", "hash": "b3b21af0a408b5614e9d792a9e66722c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersiststream.dart", "hash": "ba4b050fb9bed64eb6f6476016aeba2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/enricher/io_platform_memory.dart", "hash": "23000934827e1ad78e1b5485787e667f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_trace_origins.dart", "hash": "d7c504d081248e444b10fe3fdeecc6b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/flutter_dotenv.dart", "hash": "abad4452b42cf93d5d9ff35493cda028"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/range.dart", "hash": "a6e57cd7b87262b784eb2efe6875a329"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "c9cd996cea2334f644c74ebbdb41f7f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/pick.dart", "hash": "c60b204fb5e7d501c0addb330c88d2de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/setupapi.g.dart", "hash": "9d0390331a2be3f7c018dab8bfa589de"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/theme_provider.dart", "hash": "01a3c3cb7304c4664e6f0d39b7d9e176"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ar_messages.dart", "hash": "3da2722743e4784a131240a19f44517e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/multi_level_labels.dart", "hash": "d421e08844ff7a5446d9496c9c4e1acd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page.dart", "hash": "de67603c6b6c6f55fcd5f8b06423d29a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jdouble.dart", "hash": "d0999f83605822821bf1b18f7f6fa139"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/utils.dart", "hash": "1eb2fe31f2f21cce619f672c25b1e43f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/core_legend.dart", "hash": "6ae833526776f7980aa7bc020005414f"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/token_storage_migration.g.dart", "hash": "a29662a43faf2ec2964ee82a400f20d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-6.0.0/lib/share_plus_platform_interface.dart", "hash": "7e38424729d139f4ac1ff5183cd59303"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/orders/providers/unified_orders_providers.dart", "hash": "88008aa3c16ec51994af66409f54c08b"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/carnow_user.g.dart", "hash": "2b68dce952879e07020fe337070c726b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auth/screens/new_login_screen.dart", "hash": "59d21af75296aa18b4cf4e33685f725a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationdockpattern.dart", "hash": "dc025ebc977f56a895f49dc6d82a6d45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationvaluepattern.dart", "hash": "868fd1ae52dcd191d04c90dc4a26dfac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/element.dart", "hash": "d414c1f995780a939e1d357efa0400a1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/src/messages.g.dart", "hash": "07d545e5e568302b0453b8848be6a678"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "130ada4ea6283eb536d5d8eb0786a631"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/sequence_equal.dart", "hash": "e9e452fa340b489a49dba00eabefa3ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_response.dart", "hash": "75cb331fe4a3a9e63969911969a7c633"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/screens/edit_profile_screen.dart", "hash": "fb2f46b009b24f00bc0a35f82d83f60e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "8dedd49e916a59b6940a666481d82e10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_area_series.dart", "hash": "7353d82034ed97a64640e21f475e1716"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/router/routes/seller_routes.dart", "hash": "da52cdd80176d1f8d0d6b6efb33911a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_model.g.dart", "hash": "a75161b63c927cd30a4f8271b78bd96c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/screens/add_product_screen.dart", "hash": "e9dc9e112952308203ddba7d8fd27426"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/recorder_config.dart", "hash": "c97df630ba4f222d8a8b12713a0adb52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/builder.dart", "hash": "d2b684e31e63b6c876b2c0266705447a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/sv_messages.dart", "hash": "d2fb492f89c6314f7d8e08820e2c098c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/screenshot_event_processor.dart", "hash": "cc58900b24de5437c787d9732b436ca2"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/assets/images/logo.png", "hash": "f45e96502f36257b225bc8c2672097ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/permission_request_in_progress_exception.dart", "hash": "679db8fe68683e030815afa856663565"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/sentry_unmask_widget.dart", "hash": "82e509b2ba3631b5e3f465d8565e915d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/irunningobjecttable.dart", "hash": "dfa3a8605c6665c94b8ca2bd43827718"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/adapters/io_adapter.dart", "hash": "b7579897a220a029c3ea36d6d48b4144"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/enum.dart", "hash": "66a422b44d323303a3f8c1e3a343f8b1"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/dio_provider.g.dart", "hash": "92f706ef7537a66db3532582e8ffe57a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement3.dart", "hash": "ee2f81dc37bb6d1adb9570b7634eed77"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/router/routes/chat_routes.dart", "hash": "d1eaa914a87ac7b142697efa820e86ff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "aae059b82ff751f6e81487ef98668661"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/attribute_type.dart", "hash": "a9d570114e5a6e733fb029f6b3cffad7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/token.dart", "hash": "8006c8d72d7de5fbf9f6034104c30166"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/core.dart", "hash": "b969cd0066fa07b8082edb76d2af77e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/ole32.g.dart", "hash": "5be59a094b276fbbeb0a2255d1c45e2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/foundation.dart", "hash": "ef3bca8a6d69f93b0ea606571841a56a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "10cf10518abe4a916f2cb9ed7c4b635f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_async.dart", "hash": "255fd9cb9db57da2261cb7553da325ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipropertystore.dart", "hash": "de49c234a47c24f91be2f223476fcd44"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "307c2ee6ebc77b9995c2799e8e0bed81"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensor.dart", "hash": "9d1c0eb5292b2112e1b43affe9a2cadc"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/vehicles_details_screen.dart", "hash": "5fcdf694a6f809ab5a801e85ad7e138c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/_image_loader.dart", "hash": "af9339e8836ca91cbc9c8fd6b2de7cc6"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/providers/location_provider.g.dart", "hash": "33d7653d1662a8d3ec307e8d53f051ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "c789dd4004265224055546db82c4c7c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemcontext.dart", "hash": "ecca8d7a94b7a01ee70af109474706b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/digit.dart", "hash": "ea08cf8b71713e3535d5a2164a8bc7e1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "aff0bd5981a82f881b4ac72a321ee9c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/memory_output.dart", "hash": "54d0bd1fab938813ce3076758ba7a1cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_apiquery_l2_1_0.g.dart", "hash": "71eaaef10eca13dd60c5459f65db0e72"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "f77f6a903d346f842a7fe474e427d6a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/sample_rate_format.dart", "hash": "a1c434b5aeaae0affebaa06b53947785"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/LICENSE", "hash": "a02789da8b51e7b039db4810ec3a7d03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/permute.dart", "hash": "8171c3b0d66f560aad82b73d43393092"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_chain.dart", "hash": "7ec268e37049e5c22e226c94df1776b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "0c46b12a4e0301a199ef98521f0ed3ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+24/lib/image_picker_android.dart", "hash": "007c2b99a7ab8b0ea0ed298ac83d52b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer.dart", "hash": "8117e1fa6d39c6beca7169c752319c20"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "ce666dc6b4d730d3cb07e6bfc64a8825"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "b0f444b219eafe3ec2bb9e8a09e545f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/datetime_category_axis.dart", "hash": "063ae24f712f713ca69d72f20e8117e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/with_parent.dart", "hash": "cff1275e3376c28419f9b54215ec8b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/logarithmic_axis.dart", "hash": "200f0767345bd930e369cda20543deb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ms_my_messages.dart", "hash": "94def57680320cadc692ca68f43b1807"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/origin.dart", "hash": "e57ae28d310027a2cbc7a56ee856618e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/screens/order_management_screen.dart", "hash": "7023af9d0a6bcddb5cb53f87c311e572"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/default_mapping.dart", "hash": "72bbe921b18b48d52eb45666e3c52729"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_asset_bundle.dart", "hash": "d6bb58938086fa6d1dea0b34dd2e1511"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/declaration.dart", "hash": "7b254933211feaa1ea185b61dc9b12af"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/utils/error_handler.dart", "hash": "752ab2d0db9ae4ceef5515cfe5e72867"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/flatten.dart", "hash": "b9f39f1eac6d7a0e9964cb4c7b2cd04a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_linux-0.2.3/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/mypro/carnow/lib/navigation/models/navigation_route.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/subscription.dart", "hash": "8ab19033cc6a918c1e4f454495a9ab5f"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/errors/failure.dart", "hash": "8457136e389df1ad62b8e0896c3daa8a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "a101af17dcc01da8f97ef55242f0f167"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "fa60d1a6f81796232bc16dae4ed5f4ac"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/notification_api_service.g.dart", "hash": "4a4dbfbc109c7ad04adf66e3bf06c6b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/named_entities.dart", "hash": "c7e489fa5d00c1717fe499f3845c2abb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "8ece5be4aa5c8fa615288c4c8c5277a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/exception/io_exception_event_processor.dart", "hash": "c786b0c48c92d23899473ebe29cdc8e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/widget_states_controller.dart", "hash": "ede9db9cf36ab742109c6819a21e139a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumresources.dart", "hash": "2e130b0e6cc669eb69235f142071943e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/chain.dart", "hash": "1112185143b6fe11ce84e1f3653b2b6b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "c7d65c476f653e952aedcb0cbcab3c73"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/google_auth_service.dart", "hash": "355892b97a27493341640e85208c2c4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/stack_trace.dart", "hash": "9a478fed4f2f15993c892e33f6fd766b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/pragma.dart", "hash": "871c4029c43c6dcb8ac9ba8f7799d310"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches.dart", "hash": "3459a8a962662899e6d1ed009af8ba45"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/widgets/chat_list_item.dart", "hash": "42ad644249c8c9f785ad8af0f71c4fcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system.dart", "hash": "9007580fb76ae011692307f00e0a28f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_dashboard_stats_model.dart", "hash": "74d15d5e73c480da781a800bcf6c1400"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/empty.dart", "hash": "ac4e4c808dab498eb2d5c7f813a5006b"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/widgets/empty_state_widget.dart", "hash": "7ced3cdc7c345058fe2c4ccbba4fa66e"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/fonts/Cairo/Cairo-200.ttf", "hash": "b95de133534ef622c7594cd0c94dfc25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/stack_zone_specification.dart", "hash": "d0268b4d80612385359eadd2d6ddb257"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationscrollitempattern.dart", "hash": "a3ab60b19b4725b3ea1d1b0cb1c64451"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/theme.dart", "hash": "52b05947a1dcb617334912d79888c6b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sqlite_api.dart", "hash": "78ed7ca4d6738b92d47f19b3e557afe8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/typed/stream_subscription.dart", "hash": "63190b810e77cfebf3de760baaf59832"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ta_messages.dart", "hash": "7df089819cb9d042e16cfe58f6401ded"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "9b1cee1f8aa8b638cad928232383b02b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_format.dart", "hash": "f04fc570517ea65a792945c6521d5bad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/method_channel_url_launcher.dart", "hash": "351ed98071b53d3c2e98d376f2a65a74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/funnel_data_label.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/getuid_linux.dart", "hash": "cc4abe2eecf823ea14c55f9c5c09e203"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dispose_scope-2.1.0/LICENSE", "hash": "5930e3ae812df3347a1aa2c8ae4d3ace"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemresources.dart", "hash": "47eb0e2b093b486abe563cf677b04f31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/constant.dart", "hash": "84391293163d781c7715a32ce43b3c7f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/widgets/admin_settings_widget.dart", "hash": "59436f97e2f4238515c8fe7ec4a1d5bd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/about.dart", "hash": "4bf9cb0fbb8b0236f0f9e554c7207a4c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/favorites/screens/favorites_screen.dart", "hash": "3d975ff1fe7c25b80ff279b9230646cf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "063f2360bd47faba2c178ce7da715d92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/url_filter/io_url_filter_event_processor.dart", "hash": "83110fa6f853e6876a9bb72c8d986cb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_9.dart", "hash": "e7280a239aa04ba649fc950d8170cb3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getuid.dart", "hash": "49d6d829ae481b2570a290401389d149"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/winmm.g.dart", "hash": "34b9072869b35b15ccbe1d843fa778d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/utils.dart", "hash": "e8c1fb168963c9e062a369d72d2dad6d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "c9ab6d9cf33f78fef3ff4ad99fc73390"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "4d8781c671b7df5aadf2331931458cfb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "eaf5aa7cf4fe19db30724f637b38257a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/favorites/providers/favorites_provider.g.dart", "hash": "717b528f72ffcd8dd97d0f7ee0a8418e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "79d4fba74eb854577c9589fb33994287"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "bda2eeb24233fd6f95dc5061b8bf3dd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/gdi32.g.dart", "hash": "3235bc280cf19dc53be8f10c56d89beb"}, {"path": "/Users/<USER>/mypro/carnow/.dart_tool/flutter_build/54326045c2f5e94ebb46e599c440339e/app.dill", "hash": "6303c4cb70f6ecfc6b7ee5a599a2c86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/error_helpers.dart", "hash": "73c0a59e2d19aea71c6029f871aa9f67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/simple_name.dart", "hash": "208d1ef7a6cc2445551b3138139613bd"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/widgets/loading_indicator.dart", "hash": "babd22e28687ac0b5180025bfed29d6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parser_exception.dart", "hash": "a62996936bad6c27697a35bed070547d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/screens/screens.dart", "hash": "e23bdeb91ae7ac96c9645fdc53a38291"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/bthprops.g.dart", "hash": "0b9138f9bd3068b518494cfee8627cec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "eb89408ce23b2abcd324ea5afb05a1ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/flutter_riverpod.dart", "hash": "05100b6f82b19ef0bab59f9f174ad39e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/mn_messages.dart", "hash": "ccce344b2e8810f31cd17f9c92bd831e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/flutter_framework_feature_flag_integration.dart", "hash": "dc8535bee965cca05c19311797fbc7f9"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/screens/support_screen.dart", "hash": "7c68c889f2adb1d6a1eee487f83dac2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/http_sanitizer.dart", "hash": "e3fb5907d5e067fa63d6aa86c7cfe652"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/internal/undefined.dart", "hash": "bb00c98e50d3c71d4ab7ac7c46122f3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/regexp.dart", "hash": "10ca1bc893fd799f18a91afb7640ec26"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "3f2a39352a1c6067566f8119aa021772"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/obb3.dart", "hash": "f7fd689f4549dd97ac670c72e4d617c6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "9a67635cfd2e047d996c4840d4cb18ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/null_mapping.dart", "hash": "4bc463f9c4b5496d8918b58070c10515"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/breadcrumb.dart", "hash": "a7c516553c655799348dff629e5a687c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/renderer/io_renderer.dart", "hash": "238c187fdea02359a91d33d44e8d1772"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/providers/category_provider.g.dart", "hash": "bcffb36e242089bc49cd4a474be4ca0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/headers.dart", "hash": "12ada90523ca5fc00e317c0a59889a1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/patrol-3.18.0/LICENSE", "hash": "dc0ed204c1849749ac31938318b1a97f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_linux-0.2.3/lib/src/geolocator_linux.dart", "hash": "cca824e77d48f8e393163ee29e21666f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/frames_tracking/sentry_delayed_frames_tracker.dart", "hash": "786cdbd2862c042dd44615656de271aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hooks_riverpod-2.6.1/lib/src/consumer.dart", "hash": "c2f6791f0622a14ef6f530159974fee4"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/repositories/taxonomy_repository.dart", "hash": "ba19da56b0c19cead0ee94123c9d0570"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/single_subscription_transformer.dart", "hash": "789cc727406d0343a1dddb5018570adf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern.dart", "hash": "2108c716fd8198fa3a319a1ec6cadc9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/octicons.dart", "hash": "532f13fc153c8bce7fa94f2049cd44a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_options.dart", "hash": "5f44f436ff7b1129b18a489faab45005"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/itypeinfo.dart", "hash": "d1242664c894dd9825221a49693814f2"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_dashboard_provider.dart", "hash": "b789ac377007e78e9997a02252e59448"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatadispenserex.dart", "hash": "1a8913505e5275e2ead5a2e0752d1ac6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/rsi_indicator.dart", "hash": "10fececee910d1cf654c507477d346f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE", "hash": "9633ac2bb6bd16fe5066b9905b6f0d1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/level.dart", "hash": "49f3213e86d2bafdd814ac4df3d114ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/url_launcher_windows.dart", "hash": "792062b629f33f12bf4aa68dd6601c50"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/dsn.dart", "hash": "de6bf4fd31534cda75a1afe9d911e877"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "ac317f8ed3b04bec644817e6f60a28d7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "f60846aa76dab98607aa06c9bd6cf1dd"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/fonts/Cairo/Cairo-700.ttf", "hash": "3309a2d0fb51cb2e3335b8a56f3bbf63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/calendar_theme.dart", "hash": "415ba96d123ae67213bf2646d100ccaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/_config_io.dart", "hash": "7d313ac68ec3f410b31e39f450fdaf0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/fast_line_series.dart", "hash": "0b5fbf06164814957cf0f7d4b884a5b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system_entity.dart", "hash": "c69896f9c186aab01f7d11624f5c7d4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/quad.dart", "hash": "9a043d96e7ae40786de66219219bea4a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_flutter_options.dart", "hash": "761f4308c67a58e5dca198ffac6b99e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/src/messages.g.dart", "hash": "43f414cb7bdf200801888b9a62c38f7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/bstr.dart", "hash": "0ace55de06ef5d40b277ac8dae4d760d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "1e840a2c03797a7468018e124b957d2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/screens/admin_users_list_screen.dart", "hash": "007f55ec99b7b6ac7a806facf07519d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_random_access_file.dart", "hash": "576c23d693f7712935103974ed9312ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/multipart_file.dart", "hash": "4b7bd97845d5fc94f590ed6e58f1c1c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/exception.dart", "hash": "773da8c184ab316ec6998980a1448a1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier.dart", "hash": "e07baf43a89b4a1225ab8dab1161d2be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/style.dart", "hash": "825ec1b2847bd00ad5cd840c7ddc4d6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/x_file.dart", "hash": "d06c42e6c83be207b86412e11889266a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "39f5f34a4d3615c180c9de1bf4e8dde8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/cupertino.dart", "hash": "9b83fabf1193bf4967b740dd7a2c8852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "8e58a1e955460cf5a4ea1cea2b7606cf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "2346472ec1cfdb77f3b27d3b7af72d4c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/types.dart", "hash": "102fd2fe72902d82633e70e32ec6ea7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/calendar/hijri_date_time.dart", "hash": "d03b7855a9bf19665ae017df2944c488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/parser.dart", "hash": "14f50d28cd925e630a3c1b8205fff395"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "7821d01f98c559fcbec46a41b4df7ebf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/max_body_size.dart", "hash": "585de35d3094c200ced642c0952ea1d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/publish_subject.dart", "hash": "b2fa768bd42261fab936524dd6f1c8ae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "28d3a26c44687480bac3f72c07233bf6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/line_series.dart", "hash": "6b909ad752d4a1b565d0a79be4e5f86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/theme.dart", "hash": "17736057f90cf8ac6ebf0d505f273e2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/range_area_series.dart", "hash": "9228b309017ac9135545b235bf36d4d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_vibrant.dart", "hash": "65e61fbb4fa10372b84edf9b3a226e3e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "498db9e29a08e6fdc8aee5eeb4d204ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_async_android.dart", "hash": "5cfe2d9d61584eae2e9c8e81be1dd9c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/_internal.dart", "hash": "e9769a18bf38084504032473fa22d2ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-7.0.1/LICENSE", "hash": "b37440f2e508d0e6666a3f6796a03ecc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "9c9f1e70fac06b3e87bb33ece047c4cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/winmd_constants.dart", "hash": "16115596ace5bc18b10c61743655c625"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/models/admin_user_model.freezed.dart", "hash": "a5ac1866337988089c7aebec1dcb9cd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/text_controller.dart", "hash": "15ad74ccd0b37c111052f9651a8f23e9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "22ad3e3602e0fc7a63682e56a5aeaac0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/simple.dart", "hash": "48c19c66d9143406bfb7699ab4babf90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "d99e76320b224b4518e76f311ef4a804"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/material_community_icons.dart", "hash": "e38c9cd61abd39e4058a0a77f6e02f38"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_specification.dart", "hash": "1372358135437a4cb5079188164ae596"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol.dart", "hash": "64aec0178d3970d8fa49e55a68f1eb03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/utils.dart", "hash": "d84ae47a3c688bd889f442426f39be3e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/screens/subscription_request_screen.dart", "hash": "1c57678cec09ce90f748d68c3e18c19a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/subcategory_model.g.dart", "hash": "8925f44969576a7a7f2bc76c6087997b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_exception.dart", "hash": "12bd012ef8abbb6764d008c5813fb4d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bidi-2.0.13/LICENSE", "hash": "43cea12e1d141992f166c17e0356d667"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/bubble_series.dart", "hash": "68e21ddb56dde0d3f5a0c2f9ce83432e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iknownfolder.dart", "hash": "561daa1b637bf14aa167a49b8fc3ce6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/dwmapi.g.dart", "hash": "20290eb1c157dcb3947d9e5b420ea50e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestpackagedependency.dart", "hash": "1a04b09efdee92cd9f3e6c8f821820c0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/app/carnow_app.dart", "hash": "3f8074617dfc277c91165bf1102aae4a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/models/models.dart", "hash": "a75e29dade83a7bda065d22580c68d91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/color_transition_painter.dart", "hash": "efeca7721228f90b3859ddb395d9a380"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/screens/subscription_request_status_screen.dart", "hash": "f3e46bb5a28fcff38d8b80aa857124f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/istream.dart", "hash": "3575776abdbb8b6b6ff78edda77516b5"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/theme/app_theme.dart", "hash": "da3b7f12f88800b96d0c8a5d0250e37b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/activity_item_model.freezed.dart", "hash": "244bc28829c1623e9880ccfa2e728939"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_message.g.dart", "hash": "249dd098e0e7dea5b6d518e3a37e3fbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclock2.dart", "hash": "286726a4ae635c3cb149cd640c3c096f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immdevicecollection.dart", "hash": "a45b41e12ba5853543f707ce7dbab9d4"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/models/user_vehicle.freezed.dart", "hash": "f518b8d9acf440cf7ad35a3f15bd1ff8"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/utils/unified_logger.dart", "hash": "5d3fb6a2698b4b3281c1b7898aa5cd23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/zh_messages.dart", "hash": "21ea6c0ebefeb7f4272a4f357ae72273"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/contrast/contrast.dart", "hash": "31f8129bfae39c459df7bd2d3cc37bae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/backpressure.dart", "hash": "33135edc8fab501ab562a08b8322d832"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_application.dart", "hash": "0855bca32d598f18148a3d123c552084"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/subscription_request.g.dart", "hash": "392270b047902db71222972f6caf5064"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/quantizer_wu.dart", "hash": "ce049601b2dcdf418b7d80839ed87b7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/isolate_utils.dart", "hash": "b218f0970954e9d2c518c583d6d50a2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement6.dart", "hash": "92985c94a9a966b97c156c06ab2d5195"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader4.dart", "hash": "5a65f8839771af0fad5b2cf647703264"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/sink_base.dart", "hash": "8fec1bb0c768b230066dba96aac40ff5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/sqflite_platform_interface.dart", "hash": "beea47c079349d8e03b64a5a9dcbc7df"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "204fb623e2b782051e9bcb6e320e97c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "7d5bd66d61c58afe63c6d33ee0e421c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/widgets_binding_observer.dart", "hash": "7f39bc6192034093dfa8eb621ec2a2f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/LICENSE", "hash": "4c5a88901110f96f096d0a05cc607301"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "a32174b6de983c1652638940e75aae6a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/activity_item_model.dart", "hash": "711fdb733195bdda0f1cf642b77710ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/data.dart", "hash": "d7fab9eeba6ce2b3fae0a93d5622ac93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/dio.dart", "hash": "3059dceae50124dbd966f136c80016fe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "e1a148a465b713a6366d5a22a1425926"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/frames_tracking_integration.dart", "hash": "1f97e72474991a81bfc864cb8a8d3d41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/cached_image_widget.dart", "hash": "4310ddfcafc039210f0221a343c43164"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/subscription_error.freezed.dart", "hash": "a14112ca3fecf73d9849dac201b1a1fa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/l10n/generated_cupertino_localizations.dart", "hash": "37722feca1932410bbd9c3dea466cfa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/search_controller.dart", "hash": "8a9cc08b0d9cd36bdd798b685a72ae8b"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/widgets/notification_badge.dart", "hash": "547300e0dd644ac0f9609aa8cf52e776"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/Fontisto.ttf", "hash": "0e569864d25d0108eda475b7376c99d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_flutter-4.1.0/LICENSE", "hash": "6c189f90a66a3490dde93cebc211f9c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/capture_sink.dart", "hash": "7c57a9163e2c905ac90a6616e117766f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/funnel_chart.dart", "hash": "43a8eda1677c095bf491201b778bcbbc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "98cd866294c42f2faff3451e5ca74bfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/constants_nodoc.dart", "hash": "7c9915d304f1ce53e7350d1c32ac98d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/file_picker.dart", "hash": "950568d4b3e118c9473f790bb485fa0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/retrieve_type.dart", "hash": "550bfd92eddfc12d28a028ef44f9cedd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/lib/app_links_linux.dart", "hash": "b1a4d4681448eee5c5baf6f4ef00f61d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/notification.dart", "hash": "b9609815ffdb79eb76f2cd3a77a60906"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/user_interaction/sentry_user_interaction_widget.dart", "hash": "aa431c12710b7b7ea11b95b560feae8f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "deedcf7ee9b4e76191202e61654f9dcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispnotifysource.dart", "hash": "97fe81a282e612211e9648061d6d5dbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat.dart", "hash": "eb8f82998d7328c46b04354df987a331"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/config/local_env_config.dart", "hash": "25e94bda3172694b0e35b5afe644a528"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iinspectable.dart", "hash": "4a83689a30f6283c87f680b4c54bdd91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_trace_context.dart", "hash": "454c79c2e4c3b5a47fa8417249bfaca3"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/Feather.ttf", "hash": "a76d309774d33d9856f650bed4292a23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/position_update_exception.dart", "hash": "c9d1e5ab90e2aff40b49980d1045cb31"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "8e471191ea3b6cdd6c970bf5be4cc86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_widget.dart", "hash": "20dd5adcb482a19eeec7420c025f410f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_introspect.dart", "hash": "1d43aa18b7cd09879287a4e8ba5ea5ef"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "01aec7b419ee4a50145b3ccdd2a85fa0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "3ab9652d1101aac3b5d74a4495d860ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jboolean.dart", "hash": "ce260468a44153a02e2ae1d6d94205ac"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "a64e270c19c9e9ed0c5d9a17e0c4a5d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_annotation-2.6.1/lib/riverpod_annotation.dart", "hash": "cddf05d0409767d08b2ed1565626cf6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_object.dart", "hash": "470452529b3925fdb9a5865578558e83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/pages/material.dart", "hash": "61f9ae17975d4d233db25ee3f27633bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/delegate.dart", "hash": "5fc1a25f60cfa0a0280878377348c63c"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/repositories/base_repository.dart", "hash": "d93a3b45ae4712a0f8584840d782cf0e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/legal/screens/privacy_policy_screen.dart", "hash": "e548e44e7ca114c0d798b78fe2b2df48"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "65c7fba34475056b1ca7d0ab2c855971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/mypro/carnow/assets/icons/google_logo.svg", "hash": "edd0e34f60d7ca4a2f4ece79cff21ae3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/factory_real.dart", "hash": "d9c8a4e23312f7c116ba37296c785f61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtableitempattern.dart", "hash": "0c4386f8def5b3a82bf0b70090830314"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechvoice.dart", "hash": "38d7929920e46438585ed549abb3690a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auction/models/bid_model.dart", "hash": "f4806e9c96d39ab46a7a1eabb51b6916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/scrolling_dots_painter_with_fixed_center.dart", "hash": "14451c9c0a968e3961b725373d22d498"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/models/simple_vehicle.freezed.dart", "hash": "c2b719174d75e2712c7d10442932d1e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_object_provider.dart", "hash": "39e587e00bba5c8a7978fd25cf983cc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_introspectable.dart", "hash": "a8d03ee07caa5c7bca8609694786bbf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/constants.dart", "hash": "38214fc45e628f809e0823922392fc6c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_decoder.dart", "hash": "d16fc08d820f892bacb508cc3e45935e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/factory_platform.dart", "hash": "2441a967786bd149053b72e22172ce60"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_profile_model.g.dart", "hash": "97b44797ffb23ed8f36c3d502f4545cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "e4a748e0ab7265def948ce2f5dbce86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationgriditempattern.dart", "hash": "90879288f848e0c8bd3e17539633770a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/handler_transformer.dart", "hash": "81a6a107cbfd5dc1c55af9a93189bc5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/screenshot_integration.dart", "hash": "aa86808c0b318bb7ad371e9d0957509e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/networking/simple_api_client.dart", "hash": "2618d7f098b71eea3bc88b4eb57195a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/uk_messages.dart", "hash": "51742002c6a4604b5f85ecc74b2a197f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/section_model.g.dart", "hash": "0f71f52f0daee26cfe5f4e659f4c4482"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sql.dart", "hash": "9ab11d900c41a880b39e97693f383b5d"}, {"path": "/Users/<USER>/mypro/carnow/lib/navigation/widgets/main_layout.dart", "hash": "6401e7c8c8a11aa4634bc6896e59b1cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationproxyfactoryentry.dart", "hash": "634d273f14a099a4f0bd577979779ee1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/aabb3.dart", "hash": "4d9f681599b9aba645421097eda46139"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/package_info_plus_windows.dart", "hash": "6b6d268476b0c6b3d28f6339b57b61b6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "ffa4f7b2d5b1caccc05cf4b64021ae5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/event_sink.dart", "hash": "acfd72852e16d10d8797be366c796133"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/settable.dart", "hash": "f5f653af8a150de004f1b3ca1633bceb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sql.dart", "hash": "597e7b293e2531edc3ef788375e11c67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/sentry_screenshot_quality.dart", "hash": "f77dd4e0558386fe5aeecd18c4e46ddd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "4fa52a6cb3ac24b95e99a20d034f43c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/lv_messages.dart", "hash": "07c4da4841ab6a2c4f3aa74d6cba63ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_write_buffer.dart", "hash": "63d2768cdd6ab5a282fbb6a86c237b78"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_last.dart", "hash": "355616c9fb00a5e0ec803fffa8f33eff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxpackagereader.dart", "hash": "59137da0b55aefe8a4074891792a55b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/indicator_painter.dart", "hash": "ceb72b341c3fb56aef31307cad15ccb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/sequential.dart", "hash": "b5519514c9b9570c951c0da186030e29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/LICENSE", "hash": "abb5a1fdfd2511538e3e70557aad0ba1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/builder.dart", "hash": "7343264717127ebb7016260e9dc45319"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "03d585dfc6055d74a4668e69263afa5a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/models/user_vehicle.g.dart", "hash": "362e7b5b446bf75d496c961aee70bf36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mocktail-1.0.4/LICENSE", "hash": "0dc6cec7b4ef07b48069a9ff484a8ffe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/Feather.ttf", "hash": "a76d309774d33d9856f650bed4292a23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadataimport.dart", "hash": "754560d00f3c24825e656e9d7e03fd6a"}, {"path": "/Users/<USER>/mypro/carnow/.dart_tool/package_config_subset", "hash": "491481499f58070d0867cfaa4c0b5672"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sql_builder.dart", "hash": "1c4127d99af22e5232df8132ae79beeb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "9f05403438068337dd8f3433d2757535"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/dart_exception_type_identifier.dart", "hash": "ce3c381a85fd7594996f0475d6cfb8e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/api_response.dart", "hash": "e3daecd072be779b1c10058c2ca2fc2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_delegate.dart", "hash": "35512e89f2b31322744090b018902bab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_location.dart", "hash": "f91bd03132e9e671e87f0b9066647164"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "912b76b3e4d1ccf340ee3d2e911dfd28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationgridpattern.dart", "hash": "f4b8510296da48652b91a91857d7c71b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/file_picker_io.dart", "hash": "072c369984ef3b1b48aac3087b059e71"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "ad631d7cd122efc4862c1c084fbde716"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/pages/custom_transition_page.dart", "hash": "bd81c6cc5eb829742ceb3a955cd852d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/widgets_binding_integration.dart", "hash": "0d346fa69c944abb34e093c5531da3f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader2.dart", "hash": "9e2940d007af19bd5cf177e3be339363"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/date_time_format.dart", "hash": "a2aff0416ed5e953933c559720b669a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/pages/cupertino.dart", "hash": "671e5f26fbf94b9d5a70b14c8c494760"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_writer.dart", "hash": "0537fb6d3d370f2fd868e2654361356a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_notification_model.freezed.dart", "hash": "478d8a8bfbda3bc806dedcef33949ebd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "/Users/<USER>/mypro/carnow/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "5f5c81197b6600ad8f39bc32bbcc6341"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/config/server_urls.dart", "hash": "a200c2bbe0b263597e8290c71f016e45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/font_awesome_5_regular.dart", "hash": "5579030afb58808149cb495a272aea07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/helper_methods.dart", "hash": "0d0350902fa7b7c829baf0666f1a74dd"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/screens/admin_wallet_management_screen.dart", "hash": "4ccbed4288dfe06ca9ae08fa4249186d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "055a5c4a10cb9bc9f1e77c2c00e4ef9a"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/admin_provider.dart", "hash": "e79efa297123353a6b8604a6164df50b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "55b4fed5dadc735394ecc0e13867c2eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/span_id.dart", "hash": "d34f762869b82dddc145f7a0415a6a40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/processing.dart", "hash": "5a7bd956aa537e95be882d4809232c39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iprovideclassinfo.dart", "hash": "74801cb491652ec4ce96fe1f4646836a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/spline_series.dart", "hash": "4bff4d11e8266435b1d494923b65c617"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/time_to_initial_display_tracker.dart", "hash": "7c471728b0b937a4610e8a41e7ef4de9"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/widgets/app_loading_indicator.dart", "hash": "4a48147d4aadcfb782646afcb3044a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/logs_enricher_integration.dart", "hash": "ba02aa07ce94b4436f302ba054527b34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextpattern.dart", "hash": "8355566a31f02cb53e7f9b94d8c873ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/aabb2.dart", "hash": "8a05c4ee4d75a485389f2e5c2f6618e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/debug_image.dart", "hash": "2f404a7baa60ac43232d3f38f69ceb70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "2bc2f148be8fffe5f3a6a53fe8bc8333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/permission_denied_exception.dart", "hash": "c4c40bc2b2ff494b428e2d6ab0ed1fc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ichannelaudiovolume.dart", "hash": "623a5dbc96b4107a93ef35eb90184bb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/powrprof.g.dart", "hash": "bbfc82fc5cadc3b055adeaa481dfee0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/windows/file_picker_windows.dart", "hash": "65db62f5b2a5e78a156157a80180d1c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_core-0.6.8/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/error_bar_series.dart", "hash": "4601d3087b4105994086bfe5917e9ab8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxfactory.dart", "hash": "93d835e43f33ca5ed96e6e85a392c1e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/lib/src/types/android_position.dart", "hash": "5c0a3ec997252f64985fe42fb37fc6fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common/env.dart", "hash": "f23b1cec674b4863aec7961f4a2ae758"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/callbacks.dart", "hash": "b020749262d0d602700cd21e6f41acdb"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/history/widgets/history_item.dart", "hash": "44de767c025695363614e7b6a4c04395"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "6f516ffde1d36f8f5e8806e7811b15ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/Zocial.ttf", "hash": "2b777b88c95a123b4267b90e742bac3e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_map.dart", "hash": "56bb06e8c5f7f7892ae9eb352dd91f9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "28219fbae9045c4c3217c0f3fd6fa7ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemclassobject.dart", "hash": "20a078b2eb6ecf6b4b16bd817e30ecdc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/LICENSE", "hash": "3d853fa9263f8487dd82234ed6b56931"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/distinct_unique.dart", "hash": "32a430474c588e6a5dfb093a222e9f48"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/subcategory_products_screen.dart", "hash": "b7c5b8eba508b528d4f359bf3de6e444"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "dc037755b1140b31ffc8295fb9570cff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_winrt_l1_1_0.g.dart", "hash": "5764fde6a5cfb0402dca339562afb9cb"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/fonts/Cairo/Cairo-500.ttf", "hash": "cbf0e8f4625a63746797ef8709167c44"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "ef24f0630061f35a282b177d372c00d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/utils.dart", "hash": "7a6fe2bde5e3bf653cd473308d8402c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/src/messages.g.dart", "hash": "114597dbbcfb24754b14f8261211d90f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "8e7a6f654b6ef374af586747a3ea912b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/calendar/calendar_helper.dart", "hash": "add662556bf7d1fda1769b5acc519e92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/web_session_integration.dart", "hash": "ed0091e6f246a2dda5ea3daa0be2dbe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_request.dart", "hash": "102a77d5c78761e5502fec2ebec01e76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/path_utils.dart", "hash": "977c776bf5d295caaf8483b69f7a4b57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/expanding_dots_painter.dart", "hash": "985cc3f7036dae7307fd2c8a3410600e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "d5363426c1acae1c7410b4096cefd94d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/result.dart", "hash": "c6e362e3e6b16241c22db67cbbd6b85b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "cd7cadd0efa83f26d401a14e53964fd4"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/browse/routes/browse_routes.dart", "hash": "706d82e496f1f64c4394501829c7d83d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/connectivity_plus_platform_interface.dart", "hash": "88d5feb6f0a1ddf0cafe75a071bbcab2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "8678afc1455a658ddf2382ad887eec66"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/admin_subscription_provider.dart", "hash": "1c527dc69bb594cf626578eaa4699b69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/restartable_timer.dart", "hash": "89cdb68e09dda63e2a16d00b994387c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/config.dart", "hash": "6e1f276f9f7416f792db31fd51b3e3ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/evil_icons.dart", "hash": "614bd3f0cdbbbe4312c02774afc5b593"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jreference.dart", "hash": "07e6837bbbcbcc7743f33f82c3449861"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationspreadsheetitempattern.dart", "hash": "0b1037c34b64b5d7d84c6e578ab59974"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "94dab76e00a7b1155b15796b87ebe506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio_mixin.dart", "hash": "e103c51878b3741ffe4d81896876f3ef"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/carnow_api_service.dart", "hash": "2cfb512c4dd5de3316dedbf889a29b8b"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/FontAwesome.ttf", "hash": "b06871f281fee6b241d60582ae9369b9"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_notification_model.g.dart", "hash": "a7f726bb331da6653031c9e21d9da765"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/chat_privacy_settings_screen.dart", "hash": "1fca9c71a304e2ee91c6085af4e9b743"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/logger.dart", "hash": "610f4d6fd60c125e08d766985d536d52"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "2e0b7bb9c12ed9f989240a20a878badc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/predicate.dart", "hash": "ec001ba2712f91cadae858bfdfe622e7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/utils/shape_helper.dart", "hash": "d13eeb4df907cc1f97e4799e156c6ab8"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/models/admin_user_model.dart", "hash": "6cfdf356eb6ff0cf73650f646e873298"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationrangevaluepattern.dart", "hash": "32621d3d5949612fe2c614d37bfaf7e1"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auction/auction_localizations_extensions.dart", "hash": "35c2348db030a22753a14e484cbb7c18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/url_launcher_ios.dart", "hash": "11803ff481a58d66000cbea8c68e2af4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/category_model.freezed.dart", "hash": "e95f25ea8096ac2259871356dccf063e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/dconf_client.dart", "hash": "bb02bf9175bc337b3ca038a666521b69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/base.dart", "hash": "8e16702463aaa9f1da9da189aabae66c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/dispatcher.dart", "hash": "9de140992b1876855e65cdffbefe8a40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gsettings.dart", "hash": "ed600802105f1233acf26082c0669b92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/pattern.dart", "hash": "d881c458d06573eb887bdf0f3ce9f586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector2.dart", "hash": "6b519d909b25ca9d144af7972d689c6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-6.0.0/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "7270419a025fdbf7840e542397db0c5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_method_channel.dart", "hash": "2c294b86e9cf73bb732d8419ab47f434"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "91bf94aea1db708a8378fa41de066d33"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/account/providers/auth_providers.dart", "hash": "23b4f4f32753522a6713e63650e73d60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/frame.dart", "hash": "49286617067167600a8c7357dff1dcfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellcheckerfactory.dart", "hash": "3aa843b290b927ec2ae60e30f12b4ab2"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/repositories/product_repository.dart", "hash": "3d7ab2fe9e9b81607838f6590c79b490"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/local_auth_windows.dart", "hash": "0aeba1cff194006a9d8e502e435d4730"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/subtree_selector.dart", "hash": "e5f1007517b62683935488c5189ebc5d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_model.freezed.dart", "hash": "0095b77a16ed9f56b6b2e18afe2c414f"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/google_logo.svg", "hash": "edd0e34f60d7ca4a2f4ece79cff21ae3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/plural_rules.dart", "hash": "4b43d777bb553eecd35ca72e6d99ac3d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_request_model.g.dart", "hash": "29afca4d8250da78ee2687c8ba99f6a5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "f90beedee11a434d706e3152bfb2fd15"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "991024814d51967a20be5851be93a8e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/interval.dart", "hash": "b2a2c73b0b7b528180181e9e4e3b4e92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation3.dart", "hash": "64b70549a67d82ee25c435f5fc06ea49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/interactions/tooltip.dart", "hash": "559f3f7a11443f1752c1dff9ce521a50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeventsource.dart", "hash": "33ce76d75b24f6c7ed6ad95a422b76b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_settings.dart", "hash": "fb43cbacbb36bd207b78117d844c3248"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/fork_join.dart", "hash": "848f74750b2ecf581969c7a0dd4c2c36"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "b3019bcd49ebc4edd28b985af11a4292"}, {"path": "/Users/<USER>/mypro/carnow/assets/data/body_styles.json", "hash": "b9626ee4b734c533f0b3c50df21acf62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/reference.dart", "hash": "e7a9dcfeea903e16ba7ddc8cc31960d8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "6aad5f436704faf509d60ddb032f41b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_queue.dart", "hash": "cf0f2c674cec774d8fc0990ee818316f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/isolate_error_integration.dart", "hash": "4ce58625d709ca5d21fc1e4ec1f8f372"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/models/location_settings.dart", "hash": "6a71940bcc46e93aee4bc1ca944037fc"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/archived_chats_screen.dart", "hash": "f48d71f452462506481c080036a9f3a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/accessories_details_screen.dart", "hash": "954f44f1fa87af1bf5a94c328c511d15"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/lock_extension.dart", "hash": "92197f660f809dbb94c7d3d67b9f24e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_controller.dart", "hash": "30b3454341d40c187ec21020db3a495b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "4349dd08c33e677b65d9e00f13c35d2e"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/errors/result.dart", "hash": "b4b38dd636f2d404c4adec2e4da69c68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/localizations.dart", "hash": "bf1918c6db450b76141f2f952babc8b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtablepattern.dart", "hash": "6a38c376b8edbead42348c54f9f12420"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/deduplication_event_processor.dart", "hash": "38ce8b79224ff0e6ec8e36f28db94c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/intersection_result.dart", "hash": "832666b4f69945b957b6399ec677085b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/license.dart", "hash": "a7afa9732f8f4a0bcf1ca2012f613943"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/set_string_array.dart", "hash": "dce5e400c1f0958583196f9db05de7b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/async.dart", "hash": "13c2765ada00f970312dd9680a866556"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/FontAwesome.ttf", "hash": "b06871f281fee6b241d60582ae9369b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/default.dart", "hash": "7f30d05e05b047b274b1c4b45391d698"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/pyramid_series.dart", "hash": "7640d3bc8a42c848423d243478a28f1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqflite.dart", "hash": "f7be2d6ca06ea6e4deeee0e441b71d6a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/base.dart", "hash": "34d65aad713399e0e95c6d52aea92d88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jfloat.dart", "hash": "95435413eebd2504fb5cee176d122df5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/font_awesome_5_brands.dart", "hash": "3009ce8967e85c95f7bc8ad85556c45c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "bce1e8ef07d9830bbf99031d77e0b9fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/not.dart", "hash": "be4332e6d8c10f4a290e2a412399e1cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/platform_exception.dart", "hash": "89ca6560d39efc4e7a136aafd44f8e49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.g.dart", "hash": "7ff35a1db7f2b80a156d464b075a09f3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/Users/<USER>/mypro/carnow/lib/l10n/app_localizations_en.dart", "hash": "e39c5e1eab58bd4faf628aac1af53b62"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/models/simple_vehicle.dart", "hash": "209f7ecb48e19e9a0c48f9c6327c9f00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/customizable_effect.dart", "hash": "31aabc9e4018bf2c1f24f38bdf0b04c0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "57d74766f36a3d72789bc7466ae44dba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/time_interval.dart", "hash": "17db414327e1f763806c112c6f664ca8"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_make.freezed.dart", "hash": "8024d4219aeef8146cb54325792f6a08"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/archived_chats_provider.g.dart", "hash": "d05124be4e993334b1a7078d1639a0d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/hr_messages.dart", "hash": "dbd2ba4fc59d8c8ab6e4cfa52bc4f4ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader.dart", "hash": "0a9121d736af630bee92bd8372e06916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_async_foundation.dart", "hash": "282aeeb78f4a92064354b5fe98161484"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/libgtk.g.dart", "hash": "de89a41a7a225cbea0748a62fc498c57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/tracing_client.dart", "hash": "cf51ffc9932b9136c95a00e8f6280c7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/hilo_series.dart", "hash": "6cdde4c110b1a146f11ffafb88b11236"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/log_event.dart", "hash": "30c8223ffe2768eb8917d150bb063a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isupporterrorinfo.dart", "hash": "0fe168f7fefcc6e38cea5a1daaa08fe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/buffer.dart", "hash": "15563ca80dc06490676ca80b6b98718f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2c582bec6fc77f68c975f84d2252ed8d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "266a40131c9f05494e82934fd7096ed0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/LICENSE", "hash": "b401650d80149b34293d0dafdf086866"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/date_symbols.dart", "hash": "83e1307f3d3d50e9d6692543e689f91a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider.dart", "hash": "edc6185b4e4994b45acda6675696d87b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/flutter_enricher_event_processor.dart", "hash": "5df6d3f1c0247cec37eafa8c3facefdd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/variant.dart", "hash": "0564ee9e759fe52b58de8af3d5d0f9b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pdf-3.11.3/LICENSE", "hash": "5335066555b14d832335aa4660d6c376"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_value.dart", "hash": "002be4c072c0cc5c5e72b5ff6d0c490b"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/config/env_config.dart", "hash": "a41f9288630cd6a7b161fc6f4c1c9e8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/os_utils.dart", "hash": "1366ff26eab529ee8a449a855785d96f"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/location_provider.g.dart", "hash": "33d7653d1662a8d3ec307e8d53f051ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/lazy.dart", "hash": "128e022b683572b60bce0c93cd05007c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_encoder.dart", "hash": "df916478c93bc4bc0bc9adb2cc286153"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/admin_alerts_summary.dart", "hash": "c2e0d1077ed4800804e36a28c396b156"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/log_record.dart", "hash": "703c5e391948c58228960d4941618099"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_run_zoned_guarded.dart", "hash": "8a8c2c276ed62faf841268a5ec4a06a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/Octicons.ttf", "hash": "4bab0c61f141522a17c97148aee365f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_trace.dart", "hash": "d75954340a0c7770eb9a149f7994598e"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/config/session_persistence_config.dart", "hash": "1465b3de6390746ebcbb3d7f6af3ac8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/environment/keys.dart", "hash": "549be845b3d9bc53502555fdb16c4cec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "96b4be28e9cb48156c65de35d7ccefba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/Entypo.ttf", "hash": "31b5ffea3daddc69dd01a1f3d6cf63c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/LICENSE", "hash": "3dcd6c4e98c9df524fe39c0c979a5300"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gsettings_backend.dart", "hash": "c0507ce5934c4fc85101f9557a7e2e1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationboolcondition.dart", "hash": "7d8e8a43fd286d637f95a0510b0d3c84"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "78a0faeef5f0e801943acdca3f98393d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/cancel_token.dart", "hash": "c9f037c19c2e4adfe331d9e56f2e72c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/failed_request_client.dart", "hash": "cdb4b0ca597fbed4e26044f1d526a9a6"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/blocked_users_management_screen.dart", "hash": "fa0ce40db4f721f77fcaf1be19154d29"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/client_reports/client_report_recorder.dart", "hash": "1039b6fc4459366a776c37fa7a2a0870"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/dv_messages.dart", "hash": "83608e8582ac2172c5d25970c1d5bfe8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "990244fbee5d6f551e98a4bcce092389"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "b656f459fa4dd04f817455858d3dd20f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_template_string.dart", "hash": "19feaaa425491d4a2639d12100f572b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_options.dart", "hash": "44005c1b9f4a2f37139637ce53b7bcc7"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/simple_product_provider.g.dart", "hash": "96e922588f6828b25fe9d70e3537b0a1"}, {"path": "/Users/<USER>/mypro/carnow/fonts/Cairo/Cairo-900.ttf", "hash": "e3e8603268d04e8943565b432404f5ec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_extensions.dart", "hash": "3a2d505268f5446e5f7694776b69b407"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/family.dart", "hash": "18d9d372c2f7421114cc2a2df21d8206"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/log_level.dart", "hash": "4c243a6ca83ee01bb17db0d0a77c681f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+24/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "93c17b2980fc5498f3ba266f24c6b93b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object.dart", "hash": "0cb51131f14d4d8df95aee83e4931780"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "0c402ad9ba3f3e4d7f45f24b27447ec2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/lowercase.dart", "hash": "044ac7a861e88a6b5e7e2d2c59ccb7bd"}, {"path": "/Users/<USER>/mypro/carnow/.env", "hash": "ea56ad479491ea036244f04df39c416b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/triangle.dart", "hash": "e3f9a51488bca91a3350831c8ad6722f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/networking/simple_api_client.g.dart", "hash": "6820a0112566a4bd7cc5fc3f5664ea6b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/foundation.dart", "hash": "b4a0affbd6f723dd36a2cc709535c192"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "d77516b410bc8410c6128cb39240acdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/word.dart", "hash": "05e847132bc525d82c8f22363faaab59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/material_localizations.dart", "hash": "1f02785d9578dfad29a08ad8f41b02af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/sentry_native_binding.dart", "hash": "e002a9776319b06a9ce532cfaf5e73b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/internals.dart", "hash": "5c4a5af039aad32f5ac9bdbfc1536af4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "c442be28b905f64b74f6e9f8e5903820"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/client_reports/noop_client_report_recorder.dart", "hash": "9d652d03448bc9c7b25d104727d31c32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_trace_header.dart", "hash": "f280d497399d14194ce2523a91a42d1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/fontisto.dart", "hash": "f163075413aca7665892f99b783d849c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.1.3/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "b0c6844b0af0cd0539060a0bfcbe3713"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuri.dart", "hash": "ed8502a630b1e3004b3e0469816899d7"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/models/admin_user_model.g.dart", "hash": "0c8fdf9846c1f6f2c1337e7e6fb82fe9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/event_codec.dart", "hash": "16d220671ba632751edb02e31809a2a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_bar100_series.dart", "hash": "1aedaad50c5056af8b4368f6790a0421"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrange2.dart", "hash": "6905ddd5343384c6898473c3d0a553a6"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/carnow_providers.g.dart", "hash": "2fe933728869831982d1f2bd698b15af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationcondition.dart", "hash": "0469c2fefb6084f264cd0df8bce7263a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/element_widget.dart", "hash": "312995df3e989aab18dbfbbed139b43f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_feature_flags.dart", "hash": "3a14d67d2cbafbe7794be5b6c9d8aafb"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/orders/providers/unified_orders_providers.g.dart", "hash": "3f3e65d8e1c397dac8ecc1fd83eafb18"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "33adcae8de663e2e8f8f410da7fc8023"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_conversation.dart", "hash": "da8fa536b6497f61d9336fd8ace39d9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_handle_l1_1_0.g.dart", "hash": "34336c7c021e6749ef0bd6a11e48f887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/he_messages.dart", "hash": "faaa82b4f77f68ed0a2ded42fa7da7ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/position.dart", "hash": "faedea5895c9ddd2b2c270817c61d1f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose_family.dart", "hash": "3b32647556f88ddd6d625ddc58c7691e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "a2f376b739fa28d7a71312ecf31d6465"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider_value_indicator_shape.dart", "hash": "949350c1ca059ddb517d7f4f80b21ecd"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/fonts/Cairo/Cairo-600.ttf", "hash": "d24823f3136e983e632a2d8e11c822b4"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/auth_models.freezed.dart", "hash": "7c9ffa4d110eba7096831eed547ae2ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/visitor.dart", "hash": "27780bbb98adce3f00386fc6223bf2c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/category_model.dart", "hash": "c65ab9e06d99ebba2828254a312b796d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/framework.dart", "hash": "d63ca0c723f6a99572c806b4ec989036"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/models/blocked_user_model.dart", "hash": "74cd8677c82683b24dbe3363cf4d519f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "e461dc9f79fcf6a9e4faf12c8182fb47"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/auto_parts_details_screen.dart", "hash": "d1db716dcd04af694ea18a60dc6c1401"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/util/jset.dart", "hash": "56d72e7649121ecb759f89d999f3742d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "2d3948bf5dd7b63d100270fce62fa2d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_io.dart", "hash": "7caf4c65583e594208feee7e60872fea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/enricher/io_enricher_event_processor.dart", "hash": "62b64d7eadf9aaee33e22bb3647a0a8b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/image_picker_linux.dart", "hash": "1936d57a483f9894c5b5c3087b446809"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/subscription_flow_provider.dart", "hash": "ee1ce7177c4015d2790f5a130154d49f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "9a31689295b300aa8ab12d29fb8853ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/chart_series.dart", "hash": "820faa084b89461f15a90cfde0fdc9a0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_transaction.dart", "hash": "3c505cc7edb69a9c895441318787aedd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_profile_model.freezed.dart", "hash": "a1b779b83c76832d919f7508d93b8359"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/types/auth_messages_ios.dart", "hash": "84f8da617ffbe5c187ab3340e6d17cce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/pie_series.dart", "hash": "92b3656fb63821880f099187b2bc57ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-3.0.0/lib/src/types.dart", "hash": "38520ef075a741df22b8bb74345a8e86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/src/fife.dart", "hash": "fea92554b4ff08ca6361ad4d226ddedb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/renderer/renderer.dart", "hash": "7668db415fdd439d432d23cfdf1a982d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/exception_impl.dart", "hash": "7b3fbf91245e315040bd120bc9bf51ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory.dart", "hash": "4ce56dab766f683c213da41402d17049"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "61cf3ac380d43d042f8d9b7e7f6a11e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/url_launcher_android.dart", "hash": "42d0000dd58d923eb70183595232c299"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/file_attribute.dart", "hash": "666073cafbc9e0c03a3939b99ec35aca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/l10n/generated_widgets_localizations.dart", "hash": "30ce176fb95b9e707e91560d1848c8f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/origin_io.dart", "hash": "cb848712bd13c11293ebd6b313d6124d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "e4c4603e78131a8bc950a8029d624a76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/global_state.dart", "hash": "dc4e3bf96e9c6e94879d54eaa2f81c69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/errors.dart", "hash": "2a7dd605fd24026f238835990b2af51c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/am_messages.dart", "hash": "53e91cefa593528a982f5ec0f6984a5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqlite_api.dart", "hash": "9442b7f8efe89c42cd235c4047480ce4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechobjecttokens.dart", "hash": "f87e5679793d9c81072018b428dadb8e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_bus_name.dart", "hash": "9cf807e15d1e83af4f62cdeb36582a91"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/navigation/unified_navigation_system.g.dart", "hash": "4ae8cad9c645cf0186f2dceb6ec3b5f5"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/providers/users_provider.dart", "hash": "01c63dc60dfafef726995083153e006b"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/subscription_service.g.dart", "hash": "3cf002a240f6b895e3a10c46da11c132"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "3de98898d0fea89f0e609dcbf7b69783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationspreadsheetpattern.dart", "hash": "fac91a50f448265e9a9f97994e8b529e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "262d1d2b1931deb30855b704092d3cb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/number_symbols_data.dart", "hash": "5ed0f2083353eabc56bf4593cb10bff7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_replay_options.dart", "hash": "d43310d245defe952c2d3ddfdce642f0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "2122bbdb5de249ae3f2444fe234a5afb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/url_launcher_linux.dart", "hash": "9d67bda83980287cc1100fe7fad9e05d"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/auth_interfaces.freezed.dart", "hash": "dc0e64ccc19f2677d9130884c37cb451"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/unbounded.dart", "hash": "a617a91b12a3156406da1d95552aa4a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/replay_recorder.dart", "hash": "7312c800b1e67bfd7d26005b0d60f19a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "1363e5e6d5efab4bae027262eff73765"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation4.dart", "hash": "d8b980603638367071e1f1c256ebd56f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "f9963c0de15655f08d11298175dd45fc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "cbeab9c259374c922b24d3cbd1cb6aa4"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/browse/screens/browse_screen.dart", "hash": "874e423365df6a02902e2d2a13adc13d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ru_messages.dart", "hash": "a5d96ba72526ccacf166fe370a14ea68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader6.dart", "hash": "33186ffed4f0249b40a7d6161b7c2351"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationandcondition.dart", "hash": "c3b42ddc5c69d20f4bbfb3ccb3f30ffc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/dxva2.g.dart", "hash": "9bbe69dd9a1b6e7cd87210c8fc19314e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/models/category_model.freezed.dart", "hash": "cf637bddb11bb4f3b7a3b5bb9f6173c2"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/auth_provider_initializer.g.dart", "hash": "eec60724a73e4c6dc8d1305f417d01ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jprimitives.dart", "hash": "5c00d41c3c01c4d960d9cc633363f741"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "a594e2e46c047f44912e93f2e38f4a47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/errors.dart", "hash": "8cbd679f40c3f8e0bd00dbbd6bfb8f79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_extensions.dart", "hash": "903d8536aa6c9e6926e96e9a2b449824"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat_eager.dart", "hash": "28788651dbafca42ae0d6023352274f3"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/models/admin_financial_models.g.dart", "hash": "c20bc16d1ac557d4dd7eac77a7640699"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "eca5aa939aa9722ead4b6c347fb4d11a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_parent.dart", "hash": "7f47dda6ed10e33236d465680dc8c12b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector.dart", "hash": "7ba48caa7a6a4eac8330274dae899e48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/client_reports/discarded_event.dart", "hash": "688d4972652ef9523c890db1989119fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/builders.dart", "hash": "ccd0c138d8f151e1ccec18f4ceb98f01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/step_area_series.dart", "hash": "50383da17d242d6ce07b480365fc7c94"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/utils/app_styles.dart", "hash": "4b30f93f023af7aad12f0fbd3ce3012f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/roc_indicator.dart", "hash": "13b666edda2c646459d1b7c9708e08c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/lookupmessages.dart", "hash": "d61223112b24cee380b1ba0010a81279"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/user32.g.dart", "hash": "f7d24a92e50e72cd80aab0301eef14ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/client_reports/discard_reason.dart", "hash": "44739736a0114175069b11c243e9c948"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_preview.dart", "hash": "3208b2267d4d1b0d118b8fcdd774b753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/common.dart", "hash": "33f949ceca0aa8895b2fa0ae289f42d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifileopendialog.dart", "hash": "54b556c56a02a636de1790f953f298bf"}, {"path": "/Users/<USER>/mypro/carnow/lib/shared/widgets/primary_button.dart", "hash": "b442e21316d04cf42bd51d23c076c4c2"}, {"path": "/Users/<USER>/mypro/carnow/lib/l10n/app_ar.arb", "hash": "c2e4f8083b36f48e4cf2273f8df384eb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "721fe68e34a4747334faa11e91f93523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/LICENSE", "hash": "75ba7e8a7322214ca6e449d0be23e2ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/placeholders.dart", "hash": "4ccaab1f2ffd61fd5998a2fe8a9be886"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/ionicons.dart", "hash": "a023276326a3fbc3dcef83ef7330e8fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/platform_brightness.dart", "hash": "052768434d52b833c0038a904877f884"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/notifications/services/notification_service.g.dart", "hash": "54c53bc99257d2504d7a585cf54ec9aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/reentrant_lock.dart", "hash": "7cff949e3b7ac960b63441117b2f6734"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/utils.dart", "hash": "ce30848ef1f94b243d6094ee0d740597"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/fonts/Cairo/Cairo-800.ttf", "hash": "6469ba6c1b59b4f2b07e49f516ccf664"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_7.dart", "hash": "c3e5aaaf36524bf9927e80f60f3b0bdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jbyte.dart", "hash": "4b9a24ce6e7fd30fbe1c072637405b32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/specialized_product_details_factory.dart", "hash": "0e8f934c2354775f1f9c3a62df29b1a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "e78589269f033237f43ffdc87adc47a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/connectivity/connectivity_integration.dart", "hash": "419433cbdce77f193d8ecd7916ed13c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/worm_effect.dart", "hash": "ed8656d5bd797cff8a17f7b3ac7fa1bf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "77e3a9ed54e0497465a4346f273bcccf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/feedback/sentry_logo.dart", "hash": "d585f581969c667d4af458e500013f45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/match.dart", "hash": "4ed364af833bbef55b63c29077f61818"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "1286926784ce0908d414d696a6321e9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database.dart", "hash": "66f280c66f95d03902082cdd2b4255e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/io.dart", "hash": "2c21734ae994817f0963bcea30513c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/entypo.dart", "hash": "5fcafb170b4311c7abdfbd2e655e63fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "2e074f4fb954a719546377c67cb54608"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/LICENSE", "hash": "7e84737d10b2b52a7f7813a508a126d5"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_generation.freezed.dart", "hash": "1d0b315f7fd44cfef9bca003d64332a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_to.dart", "hash": "a28073e1b0a1ffd4999c24379f1dfe02"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "d9511b6618e15c2df1d5d0ad39256ed1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/router/routes/account_routes.dart", "hash": "4bd34979de8f37df71138f91bf6831a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "865354d8941afe9359c093d59d7b282f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/spotlight.dart", "hash": "1c5de4c7533585fb6fed3c95f2aab6fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/separated_by.dart", "hash": "04b800051d2d913cafc3733ee1bb21c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/gtk.dart", "hash": "1eb05ccbc3ef77ddc0580581ec612f3a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "be7392100d4028793c499a48ed55cf29"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/admin_financial_chart.dart", "hash": "5b2f600de9105e2cc618245c5257bad2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "14177be7a74b321668af2b9effa0f396"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "6e22c7f1454c97560ef83096561678dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/async_cache.dart", "hash": "638c6d804d20c1f83790f7f10c4af408"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/c/binding.dart", "hash": "2c389ac035cc7ca8143cd0964f95c223"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/orders/models/models.dart", "hash": "31af3fdb5eb1ea41084dca1682240c4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/replay_stream.dart", "hash": "c86f575dce7f62595d9f1e169492f750"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix2.dart", "hash": "ac51c125ed5881de5309794becbacc8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/result.dart", "hash": "bc503b6c5e3658a13efaee4e0638935a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jarray.dart", "hash": "9f246e3fdb38185324a46d303de60293"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/settings/screens/developer_tools_screen.dart", "hash": "55748ad3d5cb076172a203060e730c20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/customizable_painter.dart", "hash": "682cc2e640ed76249549b363d5517dc0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "a02235e1a98989d6740067da46b4f73d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/dtd/external_id.dart", "hash": "348e54c1032cec91d7a1a5cfce8c2098"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/url_filter/url_filter_event_processor.dart", "hash": "9d66d55cbcfa9986834bba3a9e51698d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "484329e20b76c279413a7d6dc78b3223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with_many.dart", "hash": "902509bf876a10a7b6e534a1d24fb476"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/repositories/vehicle_trim_repository.dart", "hash": "9203a6d1603eb9798b8b9dc00f97f980"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/timer.dart", "hash": "24a365985ef5e526e029d73522f4f2fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/load_contexts_integration.dart", "hash": "f46e2259cbda798ec4c6935440e52ee9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/marker.dart", "hash": "f24a8c56c2d9c496039761d0427bb2dc"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/fonts/Cairo/Cairo-900.ttf", "hash": "e3e8603268d04e8943565b432404f5ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/coverage-1.15.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/chat_list_screen.dart", "hash": "e73b6c8a9f3f0ab6549ad19ed5a843f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/Users/<USER>/mypro/carnow/lib/examples/settings_test_screen.dart", "hash": "869df671478f41d2c3a8c6726971badd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/scope_observer.dart", "hash": "f21767c2cd27286e3e72111221227ae4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_children.dart", "hash": "7c666bff17f2cfae821f93f0c5e66a64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/score/score.dart", "hash": "4f05e8a4424e66fedd1798178f3fbfe4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sql_builder.dart", "hash": "389352f8e1ecdf1332ad5bcb395bf9c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio.dart", "hash": "3467899798f7f8ca82797ccde4772534"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_vector_icons/fonts/Zocial.ttf", "hash": "2b777b88c95a123b4267b90e742bac3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_tracer_finish_status.dart", "hash": "190bfec843d3a76743947b27372c7174"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "52beedf1f39de08817236aaa2a8d28c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/time_to_full_display_tracker.dart", "hash": "db044f5bd6db644bb5dd38728e3126ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_envelope.dart", "hash": "080cc01da13c5b5594a1e6d10f3a53ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationselectionitempattern.dart", "hash": "dd15fe8e4901c3c57a40bed6b0079e80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/platform/platform_io.dart", "hash": "bb7e4bee2f9cca7b7e771e5ff413108d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "a91b4b0d0d10b955e8973126cf288ea4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/time_to_display_tracker.dart", "hash": "b7136752126cc2fdcb2f566d875c7d21"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "a88d8ea7c8c98dd1d35ad2853f04efe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/collection_extensions.dart", "hash": "874c21db82e74ec1d570b48ffb1bad17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/using.dart", "hash": "f70fdb6ec3125a8d6f6fb5ea4cbac59a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor.dart", "hash": "44f7c82974eac515139b98056bea67ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/sink.dart", "hash": "87e6007f2e4468fd84513f05cafcca2d"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/cart/providers/cart_provider.g.dart", "hash": "8d7b3738d5f5b1a49d2e3a7f05dadf1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/logging.dart", "hash": "5872689884d3985685f0239a1f89f71f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_format_parser.dart", "hash": "699fa08fa71f3fd7eef0d69703106acf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_not_null.dart", "hash": "9b84f667016de96aa99b12338a4dfb57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/exception/exception_event_processor.dart", "hash": "2dddf373c7063bbf8abc5228c37c6e1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/cache_managers.dart", "hash": "d4a2cc62bec6dff9fcdc94bc868ea014"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/cart/providers/cart_provider.dart", "hash": "7b1c465665c6fd9f7df759942f905b67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "97f7922aea45c38413930285b604bf18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/sentry_mask_widget.dart", "hash": "9ca0a02d07b687cabd96a4ce9fc11431"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_stream.dart", "hash": "3473bd2c623a639ff8cc439276a3825f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iapplicationactivationmanager.dart", "hash": "c96999a0782dffe9bf8eeaf394caf3bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/LICENSE", "hash": "815ca599c9df247a0c7f619bab123dad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/getuid.dart", "hash": "49d6d829ae481b2570a290401389d149"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/favorites/providers/favorites_provider.dart", "hash": "3dc3ffa89d240777cbe28fbeef619b87"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "5cbb66bc2f7ff989a32bc1e5ce5971e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/utils/debouncer.dart", "hash": "0df729d571e4e98cf640248d90b4a24d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/type_exception.dart", "hash": "abf77351ef7991f21d4f50727b72d4ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/types.dart", "hash": "6e1f68d791e2d8ff400f5c702f9b86fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/enricher/enricher_event_processor.dart", "hash": "7de83f94360b9a4ce47dafe3ba2c2cb8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/services.dart", "hash": "0330f85971391a5f5457a20e933fe264"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "c211cb790c5fc59f5bb6dcd61e0abcab"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "256d1c386e48e198e2e0a04345221477"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "8383986e94be1a258a59af29b9217876"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "578ff911d6e70b239fd629f5a0206fd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/zocial.dart", "hash": "16856d5c5aaed9f1b9a30557f9792d84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "2baf11d03f1f50ccef5294c1fe810e25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_annotation-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationitemcontainerpattern.dart", "hash": "17cf81dd718b76ea3b1453b5f74e1cd9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hooks_riverpod-2.6.1/lib/hooks_riverpod.dart", "hash": "6773ff7e3ffaeeb51cda6f204be0c2f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object_manager.dart", "hash": "5f173a5c0de15909e95d3275051138c1"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_notifications_async_provider.g.dart", "hash": "4e4c67232f79d452f15fc2b024bb5f06"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "84f94e87e444ce4ebc562b2707348a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document_fragment.dart", "hash": "88acdeb4b5b5a9e5b057f7696935fc2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_decoder.dart", "hash": "86727853a00a22df95e85607270896f7"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_profile_provider.g.dart", "hash": "eae1724f1092b24a5f9cbf6f0a3b7385"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "dbbc7f46620d816e615bbbe67eb258e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_sysinfo_l1_2_3.g.dart", "hash": "3d2b72757d0604ae307bd71ceb16f6c0"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/widgets/product_list_item.dart", "hash": "74c40ff967b83ae248fea58bbcd60ef8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation6.dart", "hash": "a878c551495aae9f415d298f162fd19e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationpropertycondition.dart", "hash": "35abc3f166f0485c87a21f0fcecae69a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/clock.dart", "hash": "d52c28f679ecf880a21c3ba08df59267"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/widgets/loading_indicator.dart", "hash": "79b008b9ef8691dcf5f66531e804b8e0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "89ae530b1eb1ce798ec54bc9b45efdba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/declaration.dart", "hash": "0ddfa36e71f58e8be68202ab6901bfdf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "752b2b12f0829a4d0abb699adad87062"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/hint.dart", "hash": "a31ae918ad3407d6b4cc40dc2bdefa99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/flatten.dart", "hash": "481d21ef07dee6f82302a015f989b597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/factory.dart", "hash": "63fa9307c55c93f4fde0e682e7da6503"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclock.dart", "hash": "c31f7af379f7e5f653364d4a14a78750"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_computation.dart", "hash": "37837bd1379e66f38e4a7775b6084d0e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c8260e372a7e6f788963210c83c55256"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/iterable_utils.dart", "hash": "2eac9b662276d56bf1fd01d0e90e6f85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/LICENSE", "hash": "9741c346eef56131163e13b9db1241b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/base.dart", "hash": "a4eb00bf15ad2af7e8ef8480d7f26a29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/merge.dart", "hash": "c2b88768bdc9704848019fd9df8c2546"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/analytics/screens/analytics_dashboard_screen.dart", "hash": "6cedc8874b73c6807f4c2d7b2df3f1cd"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/model_year.freezed.dart", "hash": "1d57ec6f4cadacfaaf4ad96737abc69e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/_web_image_info_io.dart", "hash": "e4da90bb20b3980a03665a080c87a098"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "044d6bef26a97ada1d56ff6fe9b7cc14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/response.dart", "hash": "2a02594ad813d39d23460e2abfd2551d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay_when.dart", "hash": "064ceadd31d0b29fc59188b2c4d45db1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "1244032abcc6103795809163331238a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispvoice.dart", "hash": "a47b8729b72b77cd6b5716ed37807a11"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math.dart", "hash": "703f2b29a9faedbb501bbc2cd99ba7b5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "5666a74f3f21ee2fa9b0b2aa37360700"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/auction/widgets/bid_history_widget.dart", "hash": "268032e982444904930b62dc95fcf22c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_transaction_context.dart", "hash": "fc77ca76c491d66caf46291a48ce9510"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/tma_indicator.dart", "hash": "2d58131361cc4a46621ebb75f9f1de9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/util/consolidate_bytes.dart", "hash": "b4446a7a4d053aaa35a7bc6968b4794a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/intl.dart", "hash": "6bf6753f69763933cb1a2f210f3e7197"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_response.dart", "hash": "8a4e81e8fccc01dc69bbc847b75a31b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/scatter_series.dart", "hash": "a778b094ab0982a607e24a8d80cea757"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_builder.dart", "hash": "bc1f35bad7b3fd785bd8734292b27ff7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/temperature/temperature_cache.dart", "hash": "2e46417042b18a7fd19a9d6c90fce1b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/zooming_helper.dart", "hash": "58b208657c655340ea17e065cade5c21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/base.dart", "hash": "d0b83bff5ce65e6924939f442ae2c2a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/feature_flags_integration.dart", "hash": "626dea98b831b0aa56ab3d804fd03f4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/page_controller.dart", "hash": "d1fdb1336eb15b98f8c1d1e574162065"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/span_data_convention.dart", "hash": "1613adbb57bd7e7627bffc871cd4301f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/fr_messages.dart", "hash": "95b84a32d1c9b959bcdc760bb13b83da"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_model.freezed.dart", "hash": "efee3fd410ad00c419efaed9df31197c"}, {"path": "/Users/<USER>/mypro/carnow/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "hash": "b0618a3f73945a7f537707c74066c33e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ivirtualdesktopmanager.dart", "hash": "ffd004f95154cc4fe026271fb8aed8cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/shlwapi.g.dart", "hash": "4230059d9b32165301d8d2a329a9b40d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/petitparser.dart", "hash": "6cb32004f228090f1200484076254c7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/utils.dart", "hash": "265638946c5326d30422050d502f429f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/gsettings.dart", "hash": "cafc9b1a6eabfa1e6e1166ad3a876f27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiorenderclient.dart", "hash": "64708122ad6cca0c23373706cdb72c03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_linux-0.2.3/lib/src/geoclue_x.dart", "hash": "37f49afe421df95c7a1232eca5916ffe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/null_stream_sink.dart", "hash": "cc0ab0117e8a0a54ec3efe6d9251860e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/multipart_file/io_multipart_file.dart", "hash": "8094c68b4a15e6f73e09501aa6ff4a47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/cs_messages.dart", "hash": "d6f8ffdb18a778270cedff4dfdc41188"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gvariant_binary_codec.dart", "hash": "31990bc8070c89637617129a739f0f9a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "4e8a70d478371e0d995f080a6eaa8120"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/connector_line.dart", "hash": "9d2fe05ba05bdf27d287a5a6416e178c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_if_empty.dart", "hash": "f1236a5e582b3794b3fb2302d7297451"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "b139a58dace0b9d9a07a3523ed72ced5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/view_hierarchy/sentry_tree_walker.dart", "hash": "8f9ca037df30ed597f7fc74241693f52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/color_transition_effect.dart", "hash": "e0a269086bc0d7fe6879c51cb65a949c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationwindowpattern.dart", "hash": "f42009fc52ad811f1d34405961c63183"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/set_ansi.dart", "hash": "d30eba29d046c1a8b7f029838de6e49f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/providers/admin_user_providers.dart", "hash": "2ed948827b120ecf69589825c30b8e21"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/auth/enhanced_secure_token_storage.g.dart", "hash": "0a4c60b9ca9071b0ebf3c45640c1404b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/pairwise.dart", "hash": "e8e03ace330da6d410583717e7e5f681"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/preceding.dart", "hash": "9d5375413b37f738384990ebdd6c6285"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/sentry_screenshot_widget.dart", "hash": "2b64e56fb1b81e5c47cf3049d5d2865c"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/repositories/vehicle_model_repository.dart", "hash": "df2d54ca59a83ac1e677e275f60e156a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "0cf873bc441372ec89d746477273af13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/code.dart", "hash": "2d312745691a82b398796ad2f38ac63c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "97359ca5bc2635f947e7616f792565c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jvalues.dart", "hash": "62dda62cce8fbd9293aac108ac11d8a1"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/utils/ui_utils.dart", "hash": "c5f688e053424192086bcbb85468ae46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jobject.dart", "hash": "f7810a314c906c107da22fa325a722cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/dematerialize.dart", "hash": "6f6ced37453e06f063a482bcb9509370"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/orders/repositories/unified_orders_repository.dart", "hash": "9f481dc57dffb8c772a430954379d3d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/parser.dart", "hash": "830859b7bec94f5f922eaba151827455"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/c/sentry_native.dart", "hash": "05363a342ba87a0568f071be0d289cba"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_model.g.dart", "hash": "4764c799ca8eea89b53c502a1091e2d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/environment/_io_environment_variables.dart", "hash": "a1d8d3ff80739dcd4a36f964a934b9a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/ancestors.dart", "hash": "3f842dc9d82d8b21557bf598ff4ec83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_selection_type.dart", "hash": "dd685f95d5588b8d81d3913338ab9cd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hooks_riverpod-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/epsilon.dart", "hash": "b9283cabc57ae94b3c75f147903751fa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "05d4aeae6031730c6aa412a128f67448"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/separated.dart", "hash": "641f0dfad31a545ac6fa5515e83920fd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "5061e0737e2db44e82d8a8c12f328a48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/logger.dart", "hash": "0abc184f4138b805c17d7e37d675520a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/logging.dart", "hash": "60fd6d17602ae0c1d18e791d6b1b79cf"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/widgets/category_tile.dart", "hash": "b35e60ff3524f1631ff0997f2e30c24d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationstylespattern.dart", "hash": "a5c23bf569325f140ab7b7d88d3b683a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/diagnostic_log.dart", "hash": "6c47cf611185653f9a454aef0287d3c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/text.dart", "hash": "d3de5e8090ec30687a667fdb5e01f923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/blocked_users_provider.g.dart", "hash": "7cd6a7718e733cf3f8dcf8c53b3c4402"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/flutter_sentry_attachment.dart", "hash": "b3ed9b3feee936f65f958bd74604d226"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/behaviors/zooming.dart", "hash": "bf0d75b4b702636f698d1ad640056462"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/az_messages.dart", "hash": "383449705ab4829f1ac6f743d1f9b948"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/recommendations/screens/recommendations_screen.dart", "hash": "2a3d343edecf4d2ee8ff204aed149549"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/favorites/widgets/favorite_button.dart", "hash": "b39603367fdc8fc19fc08cb466bc4e27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "hash": "bb500500256905950683ee38c95fb238"}, {"path": "/Users/<USER>/mypro/carnow/assets/data/valvetrain_designs.json", "hash": "2ebc8cae64e448661e8f9eb359fc8078"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "2936420e0c8ddba21d283d969f5147d6"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/services/recommendation_api_service.dart", "hash": "333a694766774e53fbbbe0b8b0eff8c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/fonts/Foundation.ttf", "hash": "e20945d7c929279ef7a6f1db184a4470"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml_events.dart", "hash": "71b9da53ac40a568e55525799518d891"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/replay_subject.dart", "hash": "7929b4d3e79087536edc9cd260f8d4c7"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/recommendations/providers/recommendations_provider.dart", "hash": "5da7bfc6255288408d6fff87cefd2efd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_envelope_item_header.dart", "hash": "3944e5249666e212720fb1828fca5cce"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/_io_get_sentry_operating_system.dart", "hash": "1d4dd18ae427fd7167e2b8ef699e6e3e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/models/vehicle_data_models.freezed.dart", "hash": "2be2c81098e866fb47cb7dbd77d18cae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/nm.dart", "hash": "7494ac5a5e8b9d56894cd383fa6e9d91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/box_and_whisker_series.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/package_info_platform_interface.dart", "hash": "022ddffcb01934fc1b0912fcb38de832"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "d3de616e525e730c7b7e3beb57930993"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/palettes/tonal_palette.dart", "hash": "cfeea6435b5871fb518238f65eff45c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/lib/dynamic_color.dart", "hash": "960ac397d3bee91866544aba3e7d5391"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/propagation_context.dart", "hash": "2dbbc9a5003b53f9dc6b23d092b08293"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "78f88eba40852ba0b7700d94f3ecfec6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/node_list.dart", "hash": "4068e834e069179f5df23c7868664c19"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/product_details_screen.dart", "hash": "9d9b179524e32c9a04bcf378c5838681"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/internals.dart", "hash": "6683b2c06b0ec964286b1a54f7e2803f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/ema_indicator.dart", "hash": "64c9248a39cc5d2848d0365998ce78bc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "2458910beb2b4f3b177a7db027cf7d34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifiledialog2.dart", "hash": "ef41b02d4257a466a4a68f493052b543"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/src/network_manager_client.dart", "hash": "60838abe37c945cf06c1b5ccc5066fed"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_make.g.dart", "hash": "0389ac2c3e6214936d816a1b5c1b577a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/categories/screens/category_browser_screen.dart", "hash": "740b638e2790bcc36871a9342fac78ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/mime_converter.dart", "hash": "601a4561a6a4b9a0f99cdc39dbb67c0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessioncontrol2.dart", "hash": "d71b6121d7069ff8303334b41e9a92d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemfilter.dart", "hash": "a9a9ecd14dd90500d067ccb5c564cb22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imoniker.dart", "hash": "59c4492b4ff3d2e5424c1903bcb8a271"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/version.dart", "hash": "9b1ea527945495f5f87ddc78906dbdb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sdk_version.dart", "hash": "61e3fe731e065ff8874e175f7e91e4b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/data_label.dart", "hash": "3fa0e799f641720cb94b3f57e5eb0e0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_plugin.dart", "hash": "ae3622db94fb8368f3577f6e71f3ea4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/masking_config.dart", "hash": "f7d2177a5b83c37e2905c02dc10063b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "033b9ff4ea4c8fa3e9da4573a7d2edfb"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/subscription_response.dart", "hash": "658f6c4a39a37de774756c2543d1e6c6"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/admin_system_health.dart", "hash": "a2051330263e779c785decb00c585f0f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "f56109c40e6fe9e53f9c6ad021d25ff5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/models/carnow_transaction.dart", "hash": "d778a75273302ae85db4177e63f15ba7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/utils/timer_debouncer.dart", "hash": "5cb1c7e04dfcd126957e5da3844b740d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/replay_config.dart", "hash": "a16964e0ccf09d1cdd85a0edd5aaed94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/pattern_iterable.dart", "hash": "f0ae0acd94eb48615e14f6c4d1f5b8e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechobjecttoken.dart", "hash": "fb64eb7ccd3a66090cd698eaebe1d080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_until.dart", "hash": "22ce2f0be207fd716e4ae18e312f5cf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/utils.dart", "hash": "ebf21341320c02b09bfd8dcbfc683398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/noop_sentry_span.dart", "hash": "aa6d89e7b1c648086bef15abe92d6b5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.5.4/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/seller/providers/subscription_request_provider.dart", "hash": "a5d7ff1650fee35eb0d4f887e3ef89c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_stack_trace.dart", "hash": "7b9825fc24ad09e8fbe8056b7bebbdbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/swap_effect.dart", "hash": "4f7334a8b4f7cd85908a8bb66caece4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/route.dart", "hash": "10f5d74dc11bcc034d27aaee1c7b30b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemservices.dart", "hash": "edac48a72d161089af5eee3c0d7d0d5e"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/providers/add_car_provider.g.dart", "hash": "61bbc3a9035cfb56a669942b1ed757f6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "5ffb77551727a0b5c646196e7bf1e9bc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "62f6d0411965eefd191db935e6594f90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/entity_mapping.dart", "hash": "5abb58e10e8ea85ea5990a97ee20ae4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/error_screen.dart", "hash": "72d27451431aeaf0b4f073a66bacf00f"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/providers/garage_provider.dart", "hash": "c89acb1c6565774a2ae012827295f83e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/utils.dart", "hash": "9d122acee9d1f43dcdb2ea88fd1fc95f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/doctype.dart", "hash": "a0ff9321b483226cdbe4773e33779715"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/widgets/custom_text_field.dart", "hash": "63ab36698f0b24a72405d42f8aa84997"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/cocoa/binding.dart", "hash": "c90d1d8a68703044eea97524ab7fc0a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/src/types/apple_settings.dart", "hash": "2ac7879f9d9a899ccc53c9676ba711f9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "982099e580d09c961e693c63803f768d"}, {"path": "/Users/<USER>/mypro/carnow/lib/core/router/routes/missing_routes.dart", "hash": "e9f016f81adf4f5d0827008ddb8f26ed"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "becd419f96efe14f36f18a8c8adc82cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/scarddlg.g.dart", "hash": "ff51e95e52fd4d789e71223942d5ae23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_device.dart", "hash": "5de9b4234c869bfb7f58138e26207e64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/max.dart", "hash": "82294310993896043f681e7fd66c4e56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/mypro/carnow/lib/features/garage/screens/smart_garage_screen.dart", "hash": "81ab30d8542f76548bfc4f2e89ca8454"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/timeago.dart", "hash": "7502f43fb1cb67fd53164c69bbb3b58e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/macd_indicator.dart", "hash": "b93f76a898df7977366af1e66fb2e8cf"}]}